{"name": "prisma-client-3a05f09ce0b396edebb3c9890ac82676738f696dae43091c799cd02fb7200d04", "main": "index.js", "types": "index.d.ts", "browser": "index-browser.js", "exports": {"./package.json": "./package.json", ".": {"require": {"node": "./index.js", "edge-light": "./wasm.js", "workerd": "./wasm.js", "worker": "./wasm.js", "browser": "./index-browser.js", "default": "./index.js"}, "import": {"node": "./index.js", "edge-light": "./wasm.js", "workerd": "./wasm.js", "worker": "./wasm.js", "browser": "./index-browser.js", "default": "./index.js"}, "default": "./index.js"}, "./edge": {"types": "./edge.d.ts", "require": "./edge.js", "import": "./edge.js", "default": "./edge.js"}, "./react-native": {"types": "./react-native.d.ts", "require": "./react-native.js", "import": "./react-native.js", "default": "./react-native.js"}, "./extension": {"types": "./extension.d.ts", "require": "./extension.js", "import": "./extension.js", "default": "./extension.js"}, "./index-browser": {"types": "./index.d.ts", "require": "./index-browser.js", "import": "./index-browser.js", "default": "./index-browser.js"}, "./index": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.js", "default": "./index.js"}, "./wasm": {"types": "./wasm.d.ts", "require": "./wasm.js", "import": "./wasm.js", "default": "./wasm.js"}, "./runtime/library": {"types": "./runtime/library.d.ts", "require": "./runtime/library.js", "import": "./runtime/library.js", "default": "./runtime/library.js"}, "./runtime/binary": {"types": "./runtime/binary.d.ts", "require": "./runtime/binary.js", "import": "./runtime/binary.js", "default": "./runtime/binary.js"}, "./generator-build": {"require": "./generator-build/index.js", "import": "./generator-build/index.js", "default": "./generator-build/index.js"}, "./sql": {"require": {"types": "./sql.d.ts", "node": "./sql.js", "default": "./sql.js"}, "import": {"types": "./sql.d.ts", "node": "./sql.mjs", "default": "./sql.mjs"}, "default": "./sql.js"}, "./*": "./*"}, "version": "5.22.0", "sideEffects": false}