// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// NextAuth.js Models
model Account {
  id                String  @id @default(cuid())
  userId            String  @map("user_id")
  type              String
  provider          String
  providerAccountId String  @map("provider_account_id")
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Application Models
enum UserRole {
  READER
  AUTHOR
  ADMIN
}

enum SubscriptionTier {
  FREE
  PREMIUM
  PREMIUM_PLUS
}

enum SubscriptionStatus {
  ACTIVE
  CANCELED
  PAST_DUE
  UNPAID
  INCOMPLETE
  INCOMPLETE_EXPIRED
  TRIALING
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELED
  REFUNDED
}

enum PayoutStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
}

enum ContentType {
  NOVEL
  CHAPTER
}

enum EarningType {
  SUBSCRIPTION_REVENUE
  TIP
  BONUS
  REFERRAL
}

enum NovelStatus {
  DRAFT
  PUBLISHED
  COMPLETED
  ARCHIVED
}

enum ChapterStatus {
  DRAFT
  PUBLISHED
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime? @map("email_verified")
  image         String?
  role          UserRole  @default(READER)
  bio           String?   @db.Text
  createdAt     DateTime  @default(now()) @map("created_at")
  updatedAt     DateTime  @updatedAt @map("updated_at")

  // Monetization fields
  stripeCustomerId String? @unique @map("stripe_customer_id")
  stripeAccountId  String? @unique @map("stripe_account_id") // For Connect accounts (writers)

  // Relations
  accounts        Account[]         @relation
  sessions        Session[]         @relation
  novels          Novel[]           @relation("AuthorNovels")
  library         Library[]         @relation
  readingProgress ReadingProgress[] @relation

  // Monetization relations
  subscriptions Subscription[] @relation
  payments      Payment[]      @relation
  earnings      Earning[]      @relation
  payouts       Payout[]       @relation
  tips          Tip[]          @relation("TipSender")
  receivedTips  Tip[]          @relation("TipReceiver")

  @@map("users")
}

model Novel {
  id          String      @id @default(cuid())
  title       String
  description String?     @db.Text
  synopsis    String?     @db.Text
  coverImage  String?     @map("cover_image")
  status      NovelStatus @default(DRAFT)
  genre       String?
  tags        String[]    @default([])
  authorId    String      @map("author_id")
  createdAt   DateTime    @default(now()) @map("created_at")
  updatedAt   DateTime    @updatedAt @map("updated_at")
  publishedAt DateTime?   @map("published_at")

  // Monetization fields
  isPremium           Boolean           @default(false) @map("is_premium")
  requiredTier        SubscriptionTier? @map("required_tier")
  price               Decimal?          @db.Decimal(10, 2) // For individual purchase
  revenueSharePercent Int               @default(70) @map("revenue_share_percent") // Platform takes 30%, author gets 70%

  // Relations
  author          User              @relation("AuthorNovels", fields: [authorId], references: [id], onDelete: Cascade)
  chapters        Chapter[]         @relation
  library         Library[]         @relation
  readingProgress ReadingProgress[] @relation

  @@map("novels")
}

model Chapter {
  id        String        @id @default(cuid())
  title     String
  content   String        @db.Text
  order     Int
  status    ChapterStatus @default(DRAFT)
  novelId   String        @map("novel_id")
  createdAt DateTime      @default(now()) @map("created_at")
  updatedAt DateTime      @updatedAt @map("updated_at")

  // Monetization fields
  isPremium    Boolean           @default(false) @map("is_premium")
  requiredTier SubscriptionTier? @map("required_tier")
  price        Decimal?          @db.Decimal(10, 2) // For individual chapter purchase

  // Relations
  novel               Novel             @relation(fields: [novelId], references: [id], onDelete: Cascade)
  readingProgress     ReadingProgress[] @relation
  lastChapterProgress ReadingProgress[] @relation("LastChapterRead")

  @@unique([novelId, order])
  @@map("chapters")
}

model Library {
  id      String   @id @default(cuid())
  userId  String   @map("user_id")
  novelId String   @map("novel_id")
  addedAt DateTime @default(now()) @map("added_at")

  // Relations
  user  User  @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel Novel @relation(fields: [novelId], references: [id], onDelete: Cascade)

  @@unique([userId, novelId])
  @@map("library")
}

model ReadingProgress {
  id            String   @id @default(cuid())
  userId        String   @map("user_id")
  novelId       String   @map("novel_id")
  chapterId     String?  @map("chapter_id")
  lastChapterId String?  @map("last_chapter_id")
  progress      Float    @default(0) // Percentage (0-100)
  lastReadAt    DateTime @default(now()) @map("last_read_at")
  totalTimeRead Int      @default(0) @map("total_time_read") // in seconds
  createdAt     DateTime @default(now()) @map("created_at")
  updatedAt     DateTime @updatedAt @map("updated_at")

  // Relations
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  novel       Novel    @relation(fields: [novelId], references: [id], onDelete: Cascade)
  chapter     Chapter? @relation(fields: [chapterId], references: [id], onDelete: SetNull)
  lastChapter Chapter? @relation("LastChapterRead", fields: [lastChapterId], references: [id], onDelete: SetNull)

  @@unique([userId, novelId])
  @@map("reading_progress")
}

// Monetization Models

model SubscriptionTierConfig {
  id          String           @id @default(cuid())
  tier        SubscriptionTier @unique
  name        String
  description String?          @db.Text
  price       Decimal          @db.Decimal(10, 2) // Monthly price
  yearlyPrice Decimal?         @db.Decimal(10, 2) // Yearly price (optional discount)
  features    String[]         @default([])
  isActive    Boolean          @default(true) @map("is_active")
  createdAt   DateTime         @default(now()) @map("created_at")
  updatedAt   DateTime         @updatedAt @map("updated_at")

  // Relations
  subscriptions Subscription[] @relation

  @@map("subscription_tier_configs")
}

model Subscription {
  id                   String             @id @default(cuid())
  userId               String             @map("user_id")
  tier                 SubscriptionTier
  status               SubscriptionStatus @default(ACTIVE)
  stripeSubscriptionId String?            @unique @map("stripe_subscription_id")
  stripePriceId        String?            @map("stripe_price_id")
  currentPeriodStart   DateTime           @map("current_period_start")
  currentPeriodEnd     DateTime           @map("current_period_end")
  cancelAtPeriodEnd    Boolean            @default(false) @map("cancel_at_period_end")
  canceledAt           DateTime?          @map("canceled_at")
  trialStart           DateTime?          @map("trial_start")
  trialEnd             DateTime?          @map("trial_end")
  createdAt            DateTime           @default(now()) @map("created_at")
  updatedAt            DateTime           @updatedAt @map("updated_at")

  // Relations
  user       User                   @relation(fields: [userId], references: [id], onDelete: Cascade)
  tierConfig SubscriptionTierConfig @relation(fields: [tier], references: [tier])
  payments   Payment[]              @relation

  @@map("subscriptions")
}

model Payment {
  id              String        @id @default(cuid())
  userId          String        @map("user_id")
  subscriptionId  String?       @map("subscription_id")
  stripePaymentId String?       @unique @map("stripe_payment_id")
  amount          Decimal       @db.Decimal(10, 2)
  currency        String        @default("usd")
  status          PaymentStatus @default(PENDING)
  description     String?       @db.Text
  metadata        Json? // Additional payment metadata
  failureReason   String?       @map("failure_reason")
  refundedAmount  Decimal?      @map("refunded_amount") @db.Decimal(10, 2)
  createdAt       DateTime      @default(now()) @map("created_at")
  updatedAt       DateTime      @updatedAt @map("updated_at")

  // Relations
  user         User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  subscription Subscription? @relation(fields: [subscriptionId], references: [id], onDelete: SetNull)

  @@map("payments")
}

model Earning {
  id          String      @id @default(cuid())
  userId      String      @map("user_id") // Author who earned
  type        EarningType
  amount      Decimal     @db.Decimal(10, 2)
  currency    String      @default("usd")
  description String?     @db.Text

  // Source tracking
  sourceType     ContentType? @map("source_type") // NOVEL or CHAPTER
  sourceId       String?      @map("source_id") // Novel or Chapter ID
  subscriptionId String?      @map("subscription_id") // If from subscription
  tipId          String?      @map("tip_id") // If from tip

  // Revenue sharing
  platformFee   Decimal @map("platform_fee") @db.Decimal(10, 2)
  authorEarning Decimal @map("author_earning") @db.Decimal(10, 2)

  // Payout tracking
  payoutId  String? @map("payout_id")
  isPaidOut Boolean @default(false) @map("is_paid_out")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  payout Payout? @relation(fields: [payoutId], references: [id], onDelete: SetNull)

  @@map("earnings")
}

model Payout {
  id               String       @id @default(cuid())
  userId           String       @map("user_id") // Author receiving payout
  stripeTransferId String?      @unique @map("stripe_transfer_id")
  amount           Decimal      @db.Decimal(10, 2)
  currency         String       @default("usd")
  status           PayoutStatus @default(PENDING)
  description      String?      @db.Text
  failureReason    String?      @map("failure_reason")

  // Period tracking
  periodStart DateTime @map("period_start")
  periodEnd   DateTime @map("period_end")

  // Processing dates
  scheduledAt DateTime? @map("scheduled_at")
  processedAt DateTime? @map("processed_at")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  earnings Earning[] @relation

  @@map("payouts")
}

model Tip {
  id         String  @id @default(cuid())
  senderId   String  @map("sender_id")
  receiverId String  @map("receiver_id")
  amount     Decimal @db.Decimal(10, 2)
  currency   String  @default("usd")
  message    String? @db.Text

  // Source content (what was tipped for)
  sourceType ContentType? @map("source_type") // NOVEL or CHAPTER
  sourceId   String?      @map("source_id") // Novel or Chapter ID

  // Payment tracking
  stripePaymentId String? @unique @map("stripe_payment_id")

  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  sender   User @relation("TipSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver User @relation("TipReceiver", fields: [receiverId], references: [id], onDelete: Cascade)

  @@map("tips")
}
