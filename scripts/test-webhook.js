#!/usr/bin/env node

/**
 * Webhook Testing Script
 * Tests the Stripe webhook endpoint with sample events
 */

const crypto = require('crypto');
const https = require('https');

// Sample Stripe webhook events for testing
const sampleEvents = {
  'customer.subscription.created': {
    id: 'evt_test_webhook',
    object: 'event',
    api_version: '2020-08-27',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'sub_test_subscription',
        object: 'subscription',
        status: 'active',
        customer: 'cus_test_customer',
        current_period_start: Math.floor(Date.now() / 1000),
        current_period_end: Math.floor(Date.now() / 1000) + 2592000, // 30 days
        items: {
          data: [{
            price: {
              id: 'price_test_premium_monthly'
            }
          }]
        }
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request',
      idempotency_key: null
    },
    type: 'customer.subscription.created'
  },

  'invoice.payment_succeeded': {
    id: 'evt_test_webhook_payment',
    object: 'event',
    api_version: '2020-08-27',
    created: Math.floor(Date.now() / 1000),
    data: {
      object: {
        id: 'in_test_invoice',
        object: 'invoice',
        amount_paid: 999, // $9.99 in cents
        currency: 'usd',
        customer: 'cus_test_customer',
        subscription: 'sub_test_subscription',
        status: 'paid',
        payment_intent: 'pi_test_payment_intent'
      }
    },
    livemode: false,
    pending_webhooks: 1,
    request: {
      id: 'req_test_request_payment',
      idempotency_key: null
    },
    type: 'invoice.payment_succeeded'
  }
};

function createStripeSignature(payload, secret) {
  const timestamp = Math.floor(Date.now() / 1000);
  const payloadString = JSON.stringify(payload);
  const signedPayload = `${timestamp}.${payloadString}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signedPayload, 'utf8')
    .digest('hex');
  
  return `t=${timestamp},v1=${signature}`;
}

function testWebhookEndpoint(eventType = 'customer.subscription.created') {
  return new Promise((resolve, reject) => {
    const event = sampleEvents[eventType];
    if (!event) {
      reject(new Error(`Unknown event type: ${eventType}`));
      return;
    }

    const payload = JSON.stringify(event);
    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET || 'whsec_test_secret';
    const signature = createStripeSignature(event, webhookSecret);

    const options = {
      hostname: 'localhost',
      port: 3000,
      path: '/api/webhooks/stripe',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(payload),
        'Stripe-Signature': signature
      }
    };

    const req = https.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(payload);
    req.end();
  });
}

async function runTests() {
  console.log('🧪 Testing Stripe Webhook Endpoint');
  console.log('===================================');

  // Check if server is running
  try {
    const response = await fetch('http://localhost:3000/api/health');
    console.log('✅ Development server is running');
  } catch (error) {
    console.log('❌ Development server not running on port 3000');
    console.log('Please start the server with: npm run dev');
    return;
  }

  // Test different webhook events
  const eventsToTest = [
    'customer.subscription.created',
    'invoice.payment_succeeded'
  ];

  for (const eventType of eventsToTest) {
    try {
      console.log(`\n🔍 Testing ${eventType}...`);
      const result = await testWebhookEndpoint(eventType);
      
      if (result.statusCode === 200) {
        console.log(`✅ ${eventType}: Success`);
        console.log(`   Response: ${result.body}`);
      } else {
        console.log(`❌ ${eventType}: Failed (${result.statusCode})`);
        console.log(`   Response: ${result.body}`);
      }
    } catch (error) {
      console.log(`❌ ${eventType}: Error - ${error.message}`);
    }
  }

  console.log('\n📊 Test Summary');
  console.log('================');
  console.log('If tests pass, your webhook endpoint is working correctly!');
  console.log('If tests fail, check:');
  console.log('- Development server is running (npm run dev)');
  console.log('- Webhook endpoint exists at /api/webhooks/stripe');
  console.log('- Environment variables are configured');
}

// Health check endpoint test
async function testHealthEndpoint() {
  try {
    const response = await fetch('http://localhost:3000/api/health');
    if (response.ok) {
      console.log('✅ Health endpoint responding');
      return true;
    }
  } catch (error) {
    console.log('❌ Health endpoint not responding');
    console.log('Creating health endpoint...');
    
    // Create a simple health endpoint for testing
    const healthEndpoint = `
import { NextResponse } from "next/server"

export async function GET() {
  return NextResponse.json({ 
    status: "ok", 
    timestamp: new Date().toISOString(),
    service: "monetization-platform"
  })
}
`;
    
    require('fs').writeFileSync(
      require('path').join(process.cwd(), 'src/app/api/health/route.ts'),
      healthEndpoint
    );
    
    console.log('✅ Health endpoint created');
    return false;
  }
}

if (require.main === module) {
  const command = process.argv[2];
  
  if (command === 'health') {
    testHealthEndpoint();
  } else {
    runTests();
  }
}

module.exports = {
  testWebhookEndpoint,
  createStripeSignature,
  sampleEvents
};
