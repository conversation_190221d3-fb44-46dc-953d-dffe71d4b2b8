#!/usr/bin/env node

/**
 * Stripe Setup and Testing Script
 * This script helps with Stripe configuration and testing
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkEnvFile() {
  const envPath = path.join(process.cwd(), '.env.local');
  
  if (!fs.existsSync(envPath)) {
    log('❌ .env.local file not found!', 'red');
    log('Please create .env.local file with Stripe configuration.', 'yellow');
    return false;
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const requiredVars = [
    'STRIPE_SECRET_KEY',
    'NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY',
    'STRIPE_WEBHOOK_SECRET',
    'STRIPE_PREMIUM_MONTHLY_PRICE_ID',
    'STRIPE_PREMIUM_YEARLY_PRICE_ID',
    'STRIPE_PREMIUM_PLUS_MONTHLY_PRICE_ID',
    'STRIPE_PREMIUM_PLUS_YEARLY_PRICE_ID'
  ];

  const missingVars = requiredVars.filter(varName => 
    !envContent.includes(varName) || envContent.includes(`${varName}=`)
  );

  if (missingVars.length > 0) {
    log('❌ Missing or empty environment variables:', 'red');
    missingVars.forEach(varName => log(`  - ${varName}`, 'yellow'));
    return false;
  }

  log('✅ Environment variables configured', 'green');
  return true;
}

function checkStripeConnection() {
  try {
    log('🔍 Checking Stripe CLI connection...', 'blue');
    const result = execSync('stripe --version', { encoding: 'utf8' });
    log(`✅ Stripe CLI installed: ${result.trim()}`, 'green');
    
    // Test authentication
    try {
      execSync('stripe config --list', { encoding: 'utf8' });
      log('✅ Stripe CLI authenticated', 'green');
      return true;
    } catch (error) {
      log('❌ Stripe CLI not authenticated', 'red');
      log('Run: stripe login', 'yellow');
      return false;
    }
  } catch (error) {
    log('❌ Stripe CLI not installed', 'red');
    log('Install from: https://stripe.com/docs/stripe-cli', 'yellow');
    return false;
  }
}

function startWebhookForwarding() {
  log('🚀 Starting webhook forwarding...', 'blue');
  log('This will forward Stripe webhooks to your local server', 'cyan');
  log('Keep this running while testing', 'cyan');
  
  try {
    execSync('stripe listen --forward-to localhost:3000/api/webhooks/stripe', { 
      stdio: 'inherit' 
    });
  } catch (error) {
    log('❌ Failed to start webhook forwarding', 'red');
    log('Make sure your development server is running on port 3000', 'yellow');
  }
}

function testWebhookEndpoint() {
  log('🧪 Testing webhook endpoint...', 'blue');
  
  try {
    const response = execSync('curl -X POST http://localhost:3000/api/webhooks/stripe -H "Content-Type: application/json" -d "{}"', { 
      encoding: 'utf8' 
    });
    
    if (response.includes('error')) {
      log('✅ Webhook endpoint responding (expected error for empty payload)', 'green');
    } else {
      log('✅ Webhook endpoint responding', 'green');
    }
  } catch (error) {
    log('❌ Webhook endpoint not responding', 'red');
    log('Make sure your development server is running', 'yellow');
  }
}

function createTestSubscription() {
  log('💳 Creating test subscription...', 'blue');
  
  try {
    // This would create a test subscription using Stripe CLI
    const result = execSync('stripe subscriptions create --customer cus_test --price price_test', { 
      encoding: 'utf8' 
    });
    log('✅ Test subscription created', 'green');
    console.log(result);
  } catch (error) {
    log('❌ Failed to create test subscription', 'red');
    log('Make sure you have valid price IDs configured', 'yellow');
  }
}

function showMenu() {
  log('\n🎯 Stripe Setup and Testing Menu', 'bright');
  log('================================', 'cyan');
  log('1. Check environment configuration', 'white');
  log('2. Check Stripe CLI connection', 'white');
  log('3. Start webhook forwarding', 'white');
  log('4. Test webhook endpoint', 'white');
  log('5. Create test subscription', 'white');
  log('6. Show setup guide', 'white');
  log('0. Exit', 'white');
  log('================================', 'cyan');
}

function showSetupGuide() {
  log('\n📚 Quick Setup Guide', 'bright');
  log('===================', 'cyan');
  log('1. Create Stripe account and get API keys', 'white');
  log('2. Create subscription products and prices', 'white');
  log('3. Set up webhook endpoint', 'white');
  log('4. Configure environment variables in .env.local', 'white');
  log('5. Run database migration: npx prisma db push', 'white');
  log('6. Start development server: npm run dev', 'white');
  log('7. Test subscription flow at /pricing', 'white');
  log('\nFor detailed instructions, see: stripe-setup-guide.md', 'yellow');
}

function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    showMenu();
    return;
  }

  const command = args[0];

  switch (command) {
    case '1':
    case 'check-env':
      checkEnvFile();
      break;
    
    case '2':
    case 'check-stripe':
      checkStripeConnection();
      break;
    
    case '3':
    case 'webhook':
      startWebhookForwarding();
      break;
    
    case '4':
    case 'test-webhook':
      testWebhookEndpoint();
      break;
    
    case '5':
    case 'test-subscription':
      createTestSubscription();
      break;
    
    case '6':
    case 'guide':
      showSetupGuide();
      break;
    
    case 'all':
      log('🚀 Running complete setup check...', 'bright');
      checkEnvFile();
      checkStripeConnection();
      testWebhookEndpoint();
      break;
    
    default:
      log('❌ Unknown command', 'red');
      showMenu();
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  checkEnvFile,
  checkStripeConnection,
  testWebhookEndpoint,
  startWebhookForwarding
};
