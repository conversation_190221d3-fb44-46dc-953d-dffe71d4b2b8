"use client"

import { Badge } from "@/components/ui/badge"
import { Crown, Star, Zap } from "lucide-react"
import { SubscriptionTier } from "@prisma/client"
import { getTierDisplayName } from "@/lib/content-access"

interface PremiumBadgeProps {
  isPremium: boolean
  requiredTier?: SubscriptionTier | null
  size?: 'sm' | 'md' | 'lg'
  showIcon?: boolean
  className?: string
}

export function PremiumBadge({ 
  isPremium, 
  requiredTier, 
  size = 'md',
  showIcon = true,
  className 
}: PremiumBadgeProps) {
  if (!isPremium) {
    return null
  }

  const tier = requiredTier || SubscriptionTier.PREMIUM

  const getIcon = () => {
    if (!showIcon) return null
    
    const iconSize = size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'
    
    switch (tier) {
      case SubscriptionTier.PREMIUM:
        return <Crown className={`${iconSize} text-blue-500`} />
      case SubscriptionTier.PREMIUM_PLUS:
        return <Zap className={`${iconSize} text-purple-500`} />
      default:
        return <Star className={`${iconSize} text-yellow-500`} />
    }
  }

  const getVariant = () => {
    switch (tier) {
      case SubscriptionTier.PREMIUM:
        return "default"
      case SubscriptionTier.PREMIUM_PLUS:
        return "secondary"
      default:
        return "outline"
    }
  }

  const getSizeClass = () => {
    switch (size) {
      case 'sm':
        return 'text-xs px-1.5 py-0.5'
      case 'lg':
        return 'text-sm px-3 py-1'
      default:
        return 'text-xs px-2 py-0.5'
    }
  }

  return (
    <Badge 
      variant={getVariant()} 
      className={`inline-flex items-center gap-1 ${getSizeClass()} ${className}`}
    >
      {getIcon()}
      <span>{getTierDisplayName(tier)}</span>
    </Badge>
  )
}
