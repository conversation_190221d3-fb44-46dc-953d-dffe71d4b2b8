"use client"

import { useSession } from "next-auth/react"
import { useGetCurrentSubscriptionQuery } from "@/store/api/subscriptionsApi"
import { ContentPaywall } from "./content-paywall"
import { checkContentAccess, isContentAuthor, type UserWithSubscription } from "@/lib/content-access"
import { SubscriptionTier } from "@prisma/client"
import { ReactNode } from "react"

interface ContentGuardProps {
  children: ReactNode
  isPremium: boolean
  requiredTier?: SubscriptionTier | null
  contentType: 'novel' | 'chapter'
  contentTitle: string
  authorId: string
  authorName?: string
  className?: string
  showPreview?: boolean
  previewLength?: number
}

export function ContentGuard({
  children,
  isPremium,
  requiredTier,
  contentType,
  contentTitle,
  authorId,
  authorName,
  className,
  showPreview = false,
  previewLength = 200
}: ContentGuardProps) {
  const { data: session } = useSession()
  const { data: subscriptionData, isLoading } = useGetCurrentSubscriptionQuery()

  // If content is not premium, show it directly
  if (!isPremium) {
    return <>{children}</>
  }

  // If user is the author, always show content
  if (session?.user && isContentAuthor(session.user, authorId)) {
    return <>{children}</>
  }

  // Show loading state while fetching subscription
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  // Create user object with subscription for access check
  const userWithSubscription: UserWithSubscription | null = session?.user ? {
    ...session.user,
    subscriptions: subscriptionData?.subscription ? [subscriptionData.subscription] : []
  } as UserWithSubscription : null

  // Check content access
  const accessResult = checkContentAccess(userWithSubscription, {
    isPremium,
    requiredTier
  })

  // If user has access, show content
  if (accessResult.hasAccess) {
    return <>{children}</>
  }

  // If showing preview, render preview + paywall
  if (showPreview) {
    return (
      <div className={className}>
        <ContentPreview previewLength={previewLength}>
          {children}
        </ContentPreview>
        <div className="mt-6">
          <ContentPaywall
            accessResult={accessResult}
            contentType={contentType}
            contentTitle={contentTitle}
            authorName={authorName}
          />
        </div>
      </div>
    )
  }

  // Show paywall only
  return (
    <div className={className}>
      <ContentPaywall
        accessResult={accessResult}
        contentType={contentType}
        contentTitle={contentTitle}
        authorName={authorName}
      />
    </div>
  )
}

interface ContentPreviewProps {
  children: ReactNode
  previewLength: number
}

function ContentPreview({ children, previewLength }: ContentPreviewProps) {
  // Extract text content for preview
  const getTextContent = (node: ReactNode): string => {
    if (typeof node === 'string') return node
    if (typeof node === 'number') return node.toString()
    if (Array.isArray(node)) return node.map(getTextContent).join('')
    if (node && typeof node === 'object' && 'props' in node) {
      return getTextContent((node as any).props.children)
    }
    return ''
  }

  const textContent = getTextContent(children)
  const previewText = textContent.slice(0, previewLength)
  const isTextTruncated = textContent.length > previewLength

  return (
    <div className="relative">
      <div className="prose prose-sm max-w-none">
        {isTextTruncated ? (
          <>
            {previewText}
            {!previewText.endsWith('.') && !previewText.endsWith('!') && !previewText.endsWith('?') && '...'}
          </>
        ) : (
          children
        )}
      </div>
      
      {isTextTruncated && (
        <div className="absolute bottom-0 left-0 right-0 h-20 bg-gradient-to-t from-white to-transparent pointer-events-none" />
      )}
    </div>
  )
}
