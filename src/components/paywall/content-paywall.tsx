"use client"

import { useSession } from "next-auth/react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Crown, Lock, Star, Zap } from "lucide-react"
import { SubscriptionTier } from "@prisma/client"
import { getTierDisplayName, getAccessReasonMessage, type ContentAccessResult } from "@/lib/content-access"
import Link from "next/link"

interface ContentPaywallProps {
  accessResult: ContentAccessResult
  contentType: 'novel' | 'chapter'
  contentTitle: string
  authorName?: string
  className?: string
}

export function ContentPaywall({ 
  accessResult, 
  contentType, 
  contentTitle, 
  authorName,
  className 
}: ContentPaywallProps) {
  const { data: session } = useSession()

  const getTierIcon = (tier: SubscriptionTier) => {
    switch (tier) {
      case SubscriptionTier.PREMIUM:
        return <Crown className="h-6 w-6 text-blue-500" />
      case SubscriptionTier.PREMIUM_PLUS:
        return <Zap className="h-6 w-6 text-purple-500" />
      default:
        return <Star className="h-6 w-6 text-gray-500" />
    }
  }

  const getTierBadgeVariant = (tier: SubscriptionTier) => {
    switch (tier) {
      case SubscriptionTier.PREMIUM:
        return "default"
      case SubscriptionTier.PREMIUM_PLUS:
        return "secondary"
      default:
        return "outline"
    }
  }

  const getUpgradeMessage = () => {
    if (!session) {
      return "Sign in and subscribe to access this premium content"
    }

    if (accessResult.reason === 'no_subscription') {
      return "Subscribe to access this premium content"
    }

    if (accessResult.reason === 'insufficient_tier') {
      return `Upgrade to ${getTierDisplayName(accessResult.requiredTier!)} to access this content`
    }

    return "Get premium access to continue reading"
  }

  const getActionButton = () => {
    if (!session) {
      return (
        <div className="space-y-2">
          <Button asChild className="w-full">
            <Link href="/auth/signin">Sign In</Link>
          </Button>
          <Button asChild variant="outline" className="w-full">
            <Link href="/pricing">View Pricing</Link>
          </Button>
        </div>
      )
    }

    if (accessResult.reason === 'no_subscription' || accessResult.reason === 'insufficient_tier') {
      return (
        <Button asChild className="w-full">
          <Link href="/pricing">
            {accessResult.reason === 'insufficient_tier' ? 'Upgrade Plan' : 'Subscribe Now'}
          </Link>
        </Button>
      )
    }

    return (
      <Button asChild className="w-full">
        <Link href="/settings/subscription">Manage Subscription</Link>
      </Button>
    )
  }

  return (
    <Card className={`border-2 border-dashed border-gray-300 ${className}`}>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <div className="p-3 bg-gray-100 rounded-full">
            <Lock className="h-8 w-8 text-gray-600" />
          </div>
        </div>
        
        <CardTitle className="text-xl">Premium Content</CardTitle>
        <CardDescription className="text-base">
          {getUpgradeMessage()}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Content Info */}
        <div className="text-center space-y-2">
          <h3 className="font-semibold text-lg">{contentTitle}</h3>
          {authorName && (
            <p className="text-sm text-muted-foreground">by {authorName}</p>
          )}
          
          {accessResult.requiredTier && (
            <div className="flex items-center justify-center gap-2">
              {getTierIcon(accessResult.requiredTier)}
              <Badge variant={getTierBadgeVariant(accessResult.requiredTier)}>
                {getTierDisplayName(accessResult.requiredTier)} Required
              </Badge>
            </div>
          )}
        </div>

        <Separator />

        {/* Access Message */}
        <div className="text-center">
          <p className="text-sm text-muted-foreground">
            {getAccessReasonMessage(accessResult)}
          </p>
        </div>

        {/* Current Tier Info */}
        {accessResult.currentTier && accessResult.reason === 'insufficient_tier' && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center gap-2 mb-2">
              {getTierIcon(accessResult.currentTier)}
              <span className="font-medium">
                Your Current Plan: {getTierDisplayName(accessResult.currentTier)}
              </span>
            </div>
            <p className="text-sm text-blue-700">
              Upgrade to {getTierDisplayName(accessResult.requiredTier!)} to unlock this {contentType} and more premium content.
            </p>
          </div>
        )}

        {/* Benefits Preview */}
        <div className="space-y-3">
          <h4 className="font-medium text-center">Premium Benefits</h4>
          <ul className="space-y-2 text-sm">
            <li className="flex items-center gap-2">
              <Crown className="h-4 w-4 text-blue-500" />
              <span>Access to all premium novels and chapters</span>
            </li>
            <li className="flex items-center gap-2">
              <Star className="h-4 w-4 text-yellow-500" />
              <span>Ad-free reading experience</span>
            </li>
            <li className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-purple-500" />
              <span>Early access to new chapters</span>
            </li>
            <li className="flex items-center gap-2">
              <Crown className="h-4 w-4 text-green-500" />
              <span>Support your favorite authors</span>
            </li>
          </ul>
        </div>

        <Separator />

        {/* Action Button */}
        <div className="space-y-3">
          {getActionButton()}
          
          <p className="text-xs text-center text-muted-foreground">
            7-day free trial • Cancel anytime
          </p>
        </div>
      </CardContent>
    </Card>
  )
}
