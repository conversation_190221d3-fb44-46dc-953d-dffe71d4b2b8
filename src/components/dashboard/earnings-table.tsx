"use client"

import { Bad<PERSON> } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { formatCurrency } from "@/lib/stripe"
import { format } from "date-fns"
import { ExternalLink, Clock, CheckCircle, XCircle } from "lucide-react"
import type { EarningWithPayout } from "@/store/api/earningsApi"

interface EarningsTableProps {
  earnings: EarningWithPayout[]
  isLoading?: boolean
}

export function EarningsTable({ earnings, isLoading }: EarningsTableProps) {
  const getEarningTypeColor = (type: string) => {
    switch (type) {
      case 'SUBSCRIPTION_REVENUE':
        return 'bg-blue-100 text-blue-800'
      case 'TIP':
        return 'bg-green-100 text-green-800'
      case 'BONUS':
        return 'bg-purple-100 text-purple-800'
      case 'REFERRAL':
        return 'bg-orange-100 text-orange-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  const getEarningTypeLabel = (type: string) => {
    switch (type) {
      case 'SUBSCRIPTION_REVENUE':
        return 'Subscription'
      case 'TIP':
        return 'Tip'
      case 'BONUS':
        return 'Bonus'
      case 'REFERRAL':
        return 'Referral'
      default:
        return type
    }
  }

  const getPayoutStatus = (earning: EarningWithPayout) => {
    if (earning.isPaidOut && earning.payout) {
      switch (earning.payout.status) {
        case 'COMPLETED':
          return (
            <Badge variant="default" className="bg-green-100 text-green-800">
              <CheckCircle className="h-3 w-3 mr-1" />
              Paid
            </Badge>
          )
        case 'PROCESSING':
          return (
            <Badge variant="secondary">
              <Clock className="h-3 w-3 mr-1" />
              Processing
            </Badge>
          )
        case 'FAILED':
          return (
            <Badge variant="destructive">
              <XCircle className="h-3 w-3 mr-1" />
              Failed
            </Badge>
          )
        default:
          return (
            <Badge variant="outline">
              <Clock className="h-3 w-3 mr-1" />
              Pending
            </Badge>
          )
      }
    }

    return (
      <Badge variant="outline">
        <Clock className="h-3 w-3 mr-1" />
        Pending
      </Badge>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  if (earnings.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No earnings found</p>
        <p className="text-sm text-muted-foreground mt-1">
          Start creating premium content to earn revenue
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Platform Fee</TableHead>
            <TableHead>Your Earnings</TableHead>
            <TableHead>Status</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {earnings.map((earning) => (
            <TableRow key={earning.id}>
              <TableCell>
                <div className="text-sm">
                  {format(new Date(earning.createdAt), 'MMM d, yyyy')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {format(new Date(earning.createdAt), 'h:mm a')}
                </div>
              </TableCell>
              
              <TableCell>
                <Badge 
                  variant="outline" 
                  className={getEarningTypeColor(earning.type)}
                >
                  {getEarningTypeLabel(earning.type)}
                </Badge>
              </TableCell>
              
              <TableCell>
                <div className="max-w-xs">
                  <p className="text-sm truncate">
                    {earning.description || 'No description'}
                  </p>
                  {earning.sourceType && earning.sourceId && (
                    <p className="text-xs text-muted-foreground">
                      {earning.sourceType}: {earning.sourceId.slice(0, 8)}...
                    </p>
                  )}
                </div>
              </TableCell>
              
              <TableCell>
                <span className="font-medium">
                  {formatCurrency(Number(earning.amount))}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="text-red-600">
                  -{formatCurrency(Number(earning.platformFee))}
                </span>
              </TableCell>
              
              <TableCell>
                <span className="font-semibold text-green-600">
                  {formatCurrency(Number(earning.authorEarning))}
                </span>
              </TableCell>
              
              <TableCell>
                {getPayoutStatus(earning)}
              </TableCell>
              
              <TableCell>
                {earning.payout && (
                  <Button variant="ghost" size="sm">
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}
