"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { useToast } from "@/hooks/use-toast"
import { 
  useGetStripeConnectAccountQuery,
  useCreateStripeConnectAccountMutation,
  useCreateAccountLinkMutation,
  useGetPayoutsQuery 
} from "@/store/api/payoutsApi"
import { formatCurrency } from "@/lib/stripe"
import { 
  CreditCard, 
  ExternalLink, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  Settings,
  DollarSign
} from "lucide-react"
import { PayoutTable } from "./payout-table"

export function PayoutDashboard() {
  const { data: session } = useSession()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)

  const { data: connectAccount, isLoading: isLoadingAccount } = useGetStripeConnectAccountQuery()
  const { data: payoutsData, isLoading: isLoadingPayouts } = useGetPayoutsQuery({
    page: 1,
    limit: 10
  })

  const [createConnectAccount] = useCreateStripeConnectAccountMutation()
  const [createAccountLink] = useCreateAccountLinkMutation()

  const handleCreateAccount = async () => {
    setIsLoading(true)
    try {
      await createConnectAccount().unwrap()
      toast({
        title: "Account Created",
        description: "Stripe Connect account created successfully. Please complete the onboarding process.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to create Stripe Connect account. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleOnboarding = async () => {
    setIsLoading(true)
    try {
      const result = await createAccountLink({
        type: 'account_onboarding'
      }).unwrap()
      
      // Redirect to Stripe onboarding
      window.location.href = result.url
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to start onboarding process. Please try again.",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  const handleUpdateAccount = async () => {
    setIsLoading(true)
    try {
      const result = await createAccountLink({
        type: 'account_update'
      }).unwrap()
      
      // Redirect to Stripe account update
      window.location.href = result.url
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to open account settings. Please try again.",
        variant: "destructive",
      })
      setIsLoading(false)
    }
  }

  if (!session?.user || session.user.role !== 'AUTHOR') {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            This dashboard is only available for authors.
          </p>
        </CardContent>
      </Card>
    )
  }

  if (isLoadingAccount) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  const getAccountStatus = () => {
    if (!connectAccount?.hasAccount) {
      return {
        status: 'not_created',
        message: 'No Stripe Connect account',
        color: 'text-gray-600',
        icon: CreditCard
      }
    }

    if (!connectAccount.detailsSubmitted) {
      return {
        status: 'incomplete',
        message: 'Account setup incomplete',
        color: 'text-yellow-600',
        icon: AlertTriangle
      }
    }

    if (!connectAccount.payoutsEnabled) {
      return {
        status: 'pending',
        message: 'Payouts pending approval',
        color: 'text-blue-600',
        icon: Clock
      }
    }

    return {
      status: 'active',
      message: 'Account active',
      color: 'text-green-600',
      icon: CheckCircle
    }
  }

  const accountStatus = getAccountStatus()

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Payout Dashboard</h1>
          <p className="text-muted-foreground">
            Manage your Stripe Connect account and view payout history
          </p>
        </div>
      </div>

      {/* Account Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            Stripe Connect Account
          </CardTitle>
          <CardDescription>
            Set up your account to receive payouts from your earnings
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <accountStatus.icon className={`h-5 w-5 ${accountStatus.color}`} />
              <span className="font-medium">{accountStatus.message}</span>
            </div>
            
            {connectAccount?.accountId && (
              <Badge variant="outline">
                ID: {connectAccount.accountId.slice(-8)}
              </Badge>
            )}
          </div>

          {/* Account Requirements */}
          {connectAccount?.requirements && connectAccount.requirements.currently_due?.length > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Action required: Please complete the missing information to enable payouts.
                Missing: {connectAccount.requirements.currently_due.join(', ')}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            {!connectAccount?.hasAccount ? (
              <Button 
                onClick={handleCreateAccount}
                disabled={isLoading}
              >
                <CreditCard className="h-4 w-4 mr-2" />
                Create Stripe Account
              </Button>
            ) : !connectAccount.detailsSubmitted ? (
              <Button 
                onClick={handleOnboarding}
                disabled={isLoading}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Complete Setup
              </Button>
            ) : (
              <Button 
                variant="outline"
                onClick={handleUpdateAccount}
                disabled={isLoading}
              >
                <Settings className="h-4 w-4 mr-2" />
                Manage Account
              </Button>
            )}
          </div>

          {/* Account Capabilities */}
          {connectAccount?.hasAccount && (
            <div className="grid grid-cols-2 gap-4 pt-4 border-t">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Details Submitted</span>
                <Badge variant={connectAccount.detailsSubmitted ? "default" : "secondary"}>
                  {connectAccount.detailsSubmitted ? "Yes" : "No"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Payouts Enabled</span>
                <Badge variant={connectAccount.payoutsEnabled ? "default" : "secondary"}>
                  {connectAccount.payoutsEnabled ? "Yes" : "No"}
                </Badge>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Payout History */}
      {connectAccount?.hasAccount && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Payout History
            </CardTitle>
            <CardDescription>
              View your past and pending payouts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <PayoutTable 
              payouts={payoutsData?.data || []} 
              isLoading={isLoadingPayouts}
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
