"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { useGetEarningsAnalyticsQuery, useGetEarningsQuery } from "@/store/api/earningsApi"
import { formatCurrency } from "@/lib/stripe"
import { 
  DollarSign, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  Calendar,
  Download,
  Eye
} from "lucide-react"
import { EarningsChart } from "./earnings-chart"
import { EarningsTable } from "./earnings-table"

export function EarningsDashboard() {
  const { data: session } = useSession()
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'year'>('month')

  const { data: analytics, isLoading: isLoadingAnalytics } = useGetEarningsAnalyticsQuery()
  const { data: earningsData, isLoading: isLoadingEarnings } = useGetEarningsQuery({
    page: 1,
    limit: 10
  })

  if (!session?.user || session.user.role !== 'AUTHOR') {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            This dashboard is only available for authors.
          </p>
        </CardContent>
      </Card>
    )
  }

  if (isLoadingAnalytics) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="pt-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const stats = [
    {
      title: "Total Earnings",
      value: formatCurrency(analytics?.totalEarnings || 0),
      icon: DollarSign,
      description: "All-time earnings",
      color: "text-green-600"
    },
    {
      title: "This Month",
      value: formatCurrency(analytics?.monthlyEarnings || 0),
      icon: TrendingUp,
      description: "Current month earnings",
      color: "text-blue-600"
    },
    {
      title: "Pending Payouts",
      value: formatCurrency(analytics?.pendingPayouts || 0),
      icon: Clock,
      description: "Awaiting payout",
      color: "text-yellow-600"
    },
    {
      title: "Completed Payouts",
      value: formatCurrency(analytics?.completedPayouts || 0),
      icon: CheckCircle,
      description: "Successfully paid out",
      color: "text-green-600"
    }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Earnings Dashboard</h1>
          <p className="text-muted-foreground">
            Track your revenue, analytics, and payout history
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button variant="outline" size="sm">
            <Eye className="h-4 w-4 mr-2" />
            View Report
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <Card key={index}>
            <CardContent className="pt-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stat.description}
                  </p>
                </div>
                <div className={`p-2 rounded-full bg-gray-100 ${stat.color}`}>
                  <stat.icon className="h-5 w-5" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts and Tables */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="earnings">Earnings History</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Earnings Chart */}
            <Card>
              <CardHeader>
                <CardTitle>Earnings Trend</CardTitle>
                <CardDescription>
                  Your earnings over the last 12 months
                </CardDescription>
              </CardHeader>
              <CardContent>
                <EarningsChart data={analytics?.earningsByMonth || []} />
              </CardContent>
            </Card>

            {/* Earnings by Type */}
            <Card>
              <CardHeader>
                <CardTitle>Revenue Sources</CardTitle>
                <CardDescription>
                  Breakdown of earnings by type
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {analytics?.earningsByType && Object.entries(analytics.earningsByType).map(([type, amount]) => (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">{type.replace('_', ' ')}</Badge>
                    </div>
                    <span className="font-semibold">{formatCurrency(amount)}</span>
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="earnings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Earnings</CardTitle>
              <CardDescription>
                Your latest earnings transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <EarningsTable 
                earnings={earningsData?.data || []} 
                isLoading={isLoadingEarnings}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>
                  Key performance indicators for your content
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Average Monthly Earnings</span>
                  <span className="font-semibold">
                    {formatCurrency((analytics?.totalEarnings || 0) / 12)}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Payout Success Rate</span>
                  <span className="font-semibold">100%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Revenue Growth</span>
                  <Badge variant="default" className="text-green-600">
                    +12.5%
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Next Payout</CardTitle>
                <CardDescription>
                  Information about your upcoming payout
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm">Scheduled for end of month</span>
                </div>
                <div className="text-2xl font-bold">
                  {formatCurrency(analytics?.pendingPayouts || 0)}
                </div>
                <p className="text-sm text-muted-foreground">
                  Pending amount to be paid out
                </p>
                <Button size="sm" variant="outline" className="w-full">
                  View Payout Details
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
