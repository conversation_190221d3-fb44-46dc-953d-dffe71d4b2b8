"use client"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { formatCurrency } from "@/lib/stripe"
import { format } from "date-fns"
import { ExternalLink, Clock, CheckCircle, XCircle, AlertTriangle } from "lucide-react"
import type { PayoutWithEarnings } from "@/store/api/payoutsApi"

interface PayoutTableProps {
  payouts: PayoutWithEarnings[]
  isLoading?: boolean
}

export function PayoutTable({ payouts, isLoading }: PayoutTableProps) {
  const getPayoutStatusBadge = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return (
          <Badge variant="default" className="bg-green-100 text-green-800">
            <CheckCircle className="h-3 w-3 mr-1" />
            Completed
          </Badge>
        )
      case 'PROCESSING':
        return (
          <Badge variant="secondary">
            <Clock className="h-3 w-3 mr-1" />
            Processing
          </Badge>
        )
      case 'FAILED':
        return (
          <Badge variant="destructive">
            <XCircle className="h-3 w-3 mr-1" />
            Failed
          </Badge>
        )
      case 'PENDING':
        return (
          <Badge variant="outline">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Pending
          </Badge>
        )
      default:
        return (
          <Badge variant="outline">
            {status}
          </Badge>
        )
    }
  }

  const getPayoutStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'text-green-600'
      case 'PROCESSING':
        return 'text-blue-600'
      case 'FAILED':
        return 'text-red-600'
      case 'PENDING':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-12 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  if (payouts.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">No payouts found</p>
        <p className="text-sm text-muted-foreground mt-1">
          Payouts are processed monthly when you have earnings above the minimum threshold
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Period</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Earnings Count</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Processed</TableHead>
            <TableHead></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {payouts.map((payout) => (
            <TableRow key={payout.id}>
              <TableCell>
                <div className="text-sm">
                  {format(new Date(payout.createdAt), 'MMM d, yyyy')}
                </div>
                <div className="text-xs text-muted-foreground">
                  {format(new Date(payout.createdAt), 'h:mm a')}
                </div>
              </TableCell>
              
              <TableCell>
                <div className="text-sm">
                  {format(new Date(payout.periodStart), 'MMM d')} - {format(new Date(payout.periodEnd), 'MMM d, yyyy')}
                </div>
              </TableCell>
              
              <TableCell>
                <span className="font-semibold text-lg">
                  {formatCurrency(Number(payout.amount))}
                </span>
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{payout.earnings.length}</span>
                  <span className="text-xs text-muted-foreground">earnings</span>
                </div>
              </TableCell>
              
              <TableCell>
                {getPayoutStatusBadge(payout.status)}
              </TableCell>
              
              <TableCell>
                {payout.processedAt ? (
                  <div className="text-sm">
                    {format(new Date(payout.processedAt), 'MMM d, yyyy')}
                  </div>
                ) : (
                  <span className="text-muted-foreground text-sm">-</span>
                )}
              </TableCell>
              
              <TableCell>
                <div className="flex items-center gap-2">
                  {payout.stripeTransferId && (
                    <Button variant="ghost" size="sm" title="View in Stripe">
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {/* Summary */}
      <div className="border-t pt-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Total Payouts</p>
            <p className="font-semibold">
              {formatCurrency(
                payouts
                  .filter(p => p.status === 'COMPLETED')
                  .reduce((sum, p) => sum + Number(p.amount), 0)
              )}
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Pending</p>
            <p className="font-semibold text-yellow-600">
              {formatCurrency(
                payouts
                  .filter(p => p.status === 'PENDING' || p.status === 'PROCESSING')
                  .reduce((sum, p) => sum + Number(p.amount), 0)
              )}
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Completed</p>
            <p className="font-semibold text-green-600">
              {payouts.filter(p => p.status === 'COMPLETED').length}
            </p>
          </div>
          <div>
            <p className="text-muted-foreground">Failed</p>
            <p className="font-semibold text-red-600">
              {payouts.filter(p => p.status === 'FAILED').length}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
