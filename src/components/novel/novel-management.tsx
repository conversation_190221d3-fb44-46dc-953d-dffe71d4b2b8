"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { LoadingSpinner } from "@/components/common/loading-spinner"
import { useToast } from "@/hooks/use-toast"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  BookOpen,
  Edit,
  Trash2,
  Plus,
  MoreVertical,
  Eye,
  FileText,
  Calendar,
  Users
} from "lucide-react"
import Link from "next/link"
import { formatDate } from "@/lib/utils"
import type { Novel, Chapter, ChapterStatus } from "@prisma/client"

interface ChapterWithStatus extends Chapter {
  status: ChapterStatus
}

interface NovelWithChapters extends Novel {
  chapters?: ChapterWithStatus[]
  _count?: {
    chapters: number
  }
}

interface NovelManagementProps {
  novel: NovelWithChapters
}

export function NovelManagement({ novel }: NovelManagementProps) {
  const router = useRouter()
  const { toast } = useToast()
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)

  const handleDelete = async () => {
    try {
      setIsDeleting(true)
      
      // Call the API to delete novel
      const response = await fetch(`/api/novels/${novel.id}`, {
        method: 'DELETE',
      })

      if (!response.ok) {
        throw new Error('Failed to delete novel')
      }

      toast({
        title: "Success",
        description: "Novel deleted successfully",
      })
      
      // Redirect to dashboard
      router.push("/dashboard")
    } catch (error) {
      console.error('Error deleting novel:', error)
      toast({
        title: "Error",
        description: "Failed to delete novel. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleStatusChange = async (newStatus: "DRAFT" | "PUBLISHED") => {
    try {
      setIsUpdatingStatus(true)
      
      // Call the API to update novel status
      const response = await fetch(`/api/novels/${novel.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          status: newStatus
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to update novel status')
      }

      toast({
        title: "Success",
        description: `Novel ${newStatus.toLowerCase()} successfully`,
      })
      
      // Refresh the page to show updated status
      window.location.reload()
    } catch (error) {
      console.error('Error updating novel status:', error)
      toast({
        title: "Error",
        description: "Failed to update novel status. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Novel Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <CardTitle className="text-2xl">{novel.title}</CardTitle>
                <Badge variant={novel.status === "PUBLISHED" ? "default" : "secondary"}>
                  {novel.status.toLowerCase()}
                </Badge>
              </div>
              <CardDescription className="text-base">
                {novel.description}
              </CardDescription>
            </div>

            <div className="flex items-center gap-2">
              <Button asChild>
                <Link href={`/dashboard/novels/${novel.id}/edit`}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Novel
                </Link>
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem asChild>
                  <Link href={`/novels/${novel.id}`}>
                    <Eye className="mr-2 h-4 w-4" />
                    View Novel
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link href={`/dashboard/novels/${novel.id}/edit`}>
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Details
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                {novel.status === "DRAFT" ? (
                  <DropdownMenuItem
                    onClick={() => handleStatusChange("PUBLISHED")}
                    disabled={isUpdatingStatus}
                  >
                    <BookOpen className="mr-2 h-4 w-4" />
                    Publish Novel
                  </DropdownMenuItem>
                ) : (
                  <DropdownMenuItem
                    onClick={() => handleStatusChange("DRAFT")}
                    disabled={isUpdatingStatus}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Unpublish Novel
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <DropdownMenuItem
                      onSelect={(e) => e.preventDefault()}
                      className="text-destructive focus:text-destructive"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete Novel
                    </DropdownMenuItem>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Delete Novel</AlertDialogTitle>
                      <AlertDialogDescription>
                        Are you sure you want to delete "{novel.title}"? This action cannot be undone.
                        All chapters will also be deleted.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction
                        onClick={handleDelete}
                        disabled={isDeleting}
                        className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                      >
                        {isDeleting ? (
                          <>
                            <LoadingSpinner className="mr-2 h-4 w-4" />
                            Deleting...
                          </>
                        ) : (
                          "Delete Novel"
                        )}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="text-2xl font-bold">{novel._count?.chapters || novel.chapters?.length || 0}</div>
                <div className="text-sm text-muted-foreground">
                  {(novel._count?.chapters || novel.chapters?.length || 0) === 1 ? "Chapter" : "Chapters"}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <BookOpen className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">{novel.genre}</div>
                <div className="text-sm text-muted-foreground">Genre</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">{formatDate(novel.createdAt)}</div>
                <div className="text-sm text-muted-foreground">Created</div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-muted-foreground" />
              <div>
                <div className="text-sm font-medium">{formatDate(novel.updatedAt)}</div>
                <div className="text-sm text-muted-foreground">Updated</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chapters Management */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Chapters</CardTitle>
              <CardDescription>
                Manage your novel chapters
              </CardDescription>
            </div>
            <Button asChild>
              <Link href={`/dashboard/novels/${novel.id}/chapters/new`}>
                <Plus className="mr-2 h-4 w-4" />
                Add Chapter
              </Link>
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {(!novel.chapters || novel.chapters.length === 0) ? (
            <div className="text-center py-8 space-y-4">
              <FileText className="h-12 w-12 mx-auto text-muted-foreground opacity-50" />
              <div>
                <p className="text-muted-foreground">No chapters yet</p>
                <p className="text-sm text-muted-foreground">
                  Start writing by adding your first chapter
                </p>
              </div>
              <Button asChild>
                <Link href={`/dashboard/novels/${novel.id}/chapters/new`}>
                  <Plus className="mr-2 h-4 w-4" />
                  Add First Chapter
                </Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {[...(novel.chapters || [])]
                .sort((a, b) => a.order - b.order)
                .map((chapter) => (
                  <div
                    key={chapter.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors"
                  >
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium">
                          Chapter {chapter.order}: {chapter.title}
                        </h4>
                        <Badge variant={chapter.status === "PUBLISHED" ? "default" : "secondary"}>
                          {chapter.status?.toLowerCase() || "draft"}
                        </Badge>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(chapter.createdAt)}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/novels/${novel.id}/chapters/${chapter.id}`}>
                          <Eye className="h-4 w-4" />
                        </Link>
                      </Button>
                      <Button variant="outline" size="sm" asChild>
                        <Link href={`/dashboard/novels/${novel.id}/chapters/${chapter.id}/edit`}>
                          <Edit className="h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
