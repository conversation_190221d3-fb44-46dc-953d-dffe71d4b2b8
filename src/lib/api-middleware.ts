import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { checkContentAccess, isContentAuthor, type UserWithSubscription } from "@/lib/content-access"
import { SubscriptionStatus } from "@prisma/client"

/**
 * Middleware to check content access for API routes
 */
export async function withContentAccess(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: {
    contentId: string
    contentType: 'novel' | 'chapter'
    allowAuthor?: boolean
  }
): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions)
    const { contentId, contentType, allowAuthor = true } = options

    // Get content from database
    let content: any
    let authorId: string

    if (contentType === 'novel') {
      content = await prisma.novel.findUnique({
        where: { id: contentId },
        select: {
          id: true,
          isPremium: true,
          requiredTier: true,
          authorId: true,
        }
      })
      authorId = content?.authorId
    } else {
      content = await prisma.chapter.findUnique({
        where: { id: contentId },
        include: {
          novel: {
            select: {
              isPremium: true,
              requiredTier: true,
              authorId: true,
            }
          }
        }
      })
      authorId = content?.novel?.authorId
    }

    if (!content) {
      return NextResponse.json(
        { error: "Content not found" },
        { status: 404 }
      )
    }

    // If content is not premium, allow access
    const isPremium = contentType === 'novel' 
      ? content.isPremium 
      : content.isPremium || content.novel?.isPremium

    if (!isPremium) {
      return handler(request)
    }

    // If user is the author and author access is allowed, grant access
    if (allowAuthor && session?.user && isContentAuthor(session.user, authorId)) {
      return handler(request)
    }

    // Check subscription access
    if (!session?.user) {
      return NextResponse.json(
        { 
          error: "Authentication required",
          code: "AUTH_REQUIRED",
          message: "Please sign in to access premium content"
        },
        { status: 401 }
      )
    }

    // Get user with subscription
    const userWithSubscription = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        subscriptions: {
          where: {
            status: {
              in: [SubscriptionStatus.ACTIVE, SubscriptionStatus.TRIALING]
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          take: 1
        }
      }
    }) as UserWithSubscription | null

    if (!userWithSubscription) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      )
    }

    // Check content access
    const requiredTier = contentType === 'novel' 
      ? content.requiredTier 
      : content.requiredTier || content.novel?.requiredTier

    const accessResult = checkContentAccess(userWithSubscription, {
      isPremium,
      requiredTier
    })

    if (!accessResult.hasAccess) {
      return NextResponse.json(
        {
          error: "Premium content access required",
          code: "PREMIUM_REQUIRED",
          reason: accessResult.reason,
          requiredTier: accessResult.requiredTier,
          currentTier: accessResult.currentTier,
          message: "This content requires a premium subscription"
        },
        { status: 403 }
      )
    }

    // User has access, proceed with the request
    return handler(request)

  } catch (error) {
    console.error("Content access middleware error:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

/**
 * Helper function to extract content ID from request URL
 */
export function extractContentId(request: NextRequest, paramName = 'id'): string | null {
  const url = new URL(request.url)
  const pathSegments = url.pathname.split('/')
  
  // Find the segment after the param name or use the last segment
  const idIndex = pathSegments.findIndex(segment => segment === paramName)
  if (idIndex !== -1 && idIndex + 1 < pathSegments.length) {
    return pathSegments[idIndex + 1]
  }
  
  // Fallback to last segment if it looks like an ID
  const lastSegment = pathSegments[pathSegments.length - 1]
  if (lastSegment && lastSegment.length > 10) { // Assuming IDs are longer than 10 chars
    return lastSegment
  }
  
  return null
}

/**
 * Wrapper for novel content access
 */
export async function withNovelAccess(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>,
  novelId: string,
  allowAuthor = true
): Promise<NextResponse> {
  return withContentAccess(request, handler, {
    contentId: novelId,
    contentType: 'novel',
    allowAuthor
  })
}

/**
 * Wrapper for chapter content access
 */
export async function withChapterAccess(
  request: NextRequest,
  handler: (request: NextRequest) => Promise<NextResponse>,
  chapterId: string,
  allowAuthor = true
): Promise<NextResponse> {
  return withContentAccess(request, handler, {
    contentId: chapterId,
    contentType: 'chapter',
    allowAuthor
  })
}
