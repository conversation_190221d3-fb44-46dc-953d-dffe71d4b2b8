import { SubscriptionTier, SubscriptionStatus } from "@prisma/client"
import type { User, Subscription, Novel, Chapter } from "@prisma/client"

export interface UserWithSubscription extends User {
  subscriptions: Subscription[]
}

export interface ContentAccessResult {
  hasAccess: boolean
  reason?: 'not_premium' | 'insufficient_tier' | 'no_subscription' | 'subscription_inactive'
  requiredTier?: SubscriptionTier
  currentTier?: SubscriptionTier
}

/**
 * Check if a user has access to premium content based on their subscription
 */
export function checkContentAccess(
  user: UserWithSubscription | null,
  content: { isPremium: boolean; requiredTier?: SubscriptionTier | null }
): ContentAccessResult {
  // If content is not premium, everyone has access
  if (!content.isPremium) {
    return { hasAccess: true }
  }

  // If user is not logged in, no access to premium content
  if (!user) {
    return {
      hasAccess: false,
      reason: 'no_subscription',
      requiredTier: content.requiredTier || SubscriptionTier.PREMIUM
    }
  }

  // Get user's active subscription
  const activeSubscription = user.subscriptions.find(sub => 
    sub.status === SubscriptionStatus.ACTIVE || sub.status === SubscriptionStatus.TRIALING
  )

  // If no active subscription, no access
  if (!activeSubscription) {
    return {
      hasAccess: false,
      reason: 'no_subscription',
      requiredTier: content.requiredTier || SubscriptionTier.PREMIUM
    }
  }

  // Check if subscription tier is sufficient
  const requiredTier = content.requiredTier || SubscriptionTier.PREMIUM
  const userTier = activeSubscription.tier

  // Define tier hierarchy
  const tierHierarchy = {
    [SubscriptionTier.FREE]: 0,
    [SubscriptionTier.PREMIUM]: 1,
    [SubscriptionTier.PREMIUM_PLUS]: 2,
  }

  const userTierLevel = tierHierarchy[userTier]
  const requiredTierLevel = tierHierarchy[requiredTier]

  if (userTierLevel < requiredTierLevel) {
    return {
      hasAccess: false,
      reason: 'insufficient_tier',
      requiredTier,
      currentTier: userTier
    }
  }

  return { hasAccess: true, currentTier: userTier }
}

/**
 * Check if a user can access a specific novel
 */
export function checkNovelAccess(
  user: UserWithSubscription | null,
  novel: Novel
): ContentAccessResult {
  return checkContentAccess(user, {
    isPremium: novel.isPremium,
    requiredTier: novel.requiredTier
  })
}

/**
 * Check if a user can access a specific chapter
 */
export function checkChapterAccess(
  user: UserWithSubscription | null,
  chapter: Chapter,
  novel?: Novel
): ContentAccessResult {
  // First check chapter-level restrictions
  const chapterAccess = checkContentAccess(user, {
    isPremium: chapter.isPremium,
    requiredTier: chapter.requiredTier
  })

  // If chapter access is denied, return that result
  if (!chapterAccess.hasAccess) {
    return chapterAccess
  }

  // If novel is provided, also check novel-level restrictions
  if (novel) {
    const novelAccess = checkContentAccess(user, {
      isPremium: novel.isPremium,
      requiredTier: novel.requiredTier
    })

    if (!novelAccess.hasAccess) {
      return novelAccess
    }
  }

  return chapterAccess
}

/**
 * Get the display name for a subscription tier
 */
export function getTierDisplayName(tier: SubscriptionTier): string {
  switch (tier) {
    case SubscriptionTier.FREE:
      return 'Free'
    case SubscriptionTier.PREMIUM:
      return 'Premium'
    case SubscriptionTier.PREMIUM_PLUS:
      return 'Premium Plus'
    default:
      return tier
  }
}

/**
 * Get the access reason message for display
 */
export function getAccessReasonMessage(result: ContentAccessResult): string {
  if (result.hasAccess) {
    return 'You have access to this content'
  }

  switch (result.reason) {
    case 'no_subscription':
      return `This content requires a ${getTierDisplayName(result.requiredTier!)} subscription`
    case 'insufficient_tier':
      return `This content requires ${getTierDisplayName(result.requiredTier!)} or higher. You currently have ${getTierDisplayName(result.currentTier!)}`
    case 'subscription_inactive':
      return 'Your subscription is not active. Please check your billing information'
    default:
      return 'You do not have access to this premium content'
  }
}

/**
 * Check if user is an author of the content (authors can always access their own content)
 */
export function isContentAuthor(user: User | null, authorId: string): boolean {
  return user?.id === authorId
}
