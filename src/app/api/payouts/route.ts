import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { stripe, REVENUE_SHARE } from "@/lib/stripe"
import { UserRole, PayoutStatus } from "@prisma/client"
import { z } from "zod"

const payoutQuerySchema = z.object({
  page: z.string().optional().default("1"),
  limit: z.string().optional().default("10"),
  status: z.enum(['PENDING', 'PROCESSING', 'COMPLETED', 'FAILED']).optional(),
})

// GET /api/payouts - Get user's payouts with pagination
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const query = payoutQuerySchema.parse({
      page: searchParams.get('page') || "1",
      limit: searchParams.get('limit') || "10",
      status: searchParams.get('status') || undefined,
    })

    const page = parseInt(query.page)
    const limit = parseInt(query.limit)
    const offset = (page - 1) * limit

    // Build where clause
    const where: any = {
      userId: session.user.id,
    }

    if (query.status) {
      where.status = query.status
    }

    // Get payouts with pagination
    const [payouts, total] = await Promise.all([
      prisma.payout.findMany({
        where,
        include: {
          earnings: {
            select: {
              id: true,
              type: true,
              amount: true,
              authorEarning: true,
              createdAt: true,
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit,
      }),
      prisma.payout.count({ where })
    ])

    const pages = Math.ceil(total / limit)

    return NextResponse.json({
      data: payouts,
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      }
    })
  } catch (error) {
    console.error("Error fetching payouts:", error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}

// POST /api/payouts - Request a payout (admin only for now)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // For now, only allow admins to create payouts
    // In production, this would be automated or triggered by admin
    if (!session?.user || session.user.role !== UserRole.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { userId, periodStart, periodEnd } = body

    // Validate user exists and is an author
    const user = await prisma.user.findUnique({
      where: { id: userId, role: UserRole.AUTHOR },
    })

    if (!user) {
      return NextResponse.json({ error: "Author not found" }, { status: 404 })
    }

    // Check if user has Stripe Connect account
    if (!user.stripeAccountId) {
      return NextResponse.json(
        { error: "User must set up Stripe Connect account first" },
        { status: 400 }
      )
    }

    // Get unpaid earnings for the period
    const unpaidEarnings = await prisma.earning.findMany({
      where: {
        userId,
        isPaidOut: false,
        createdAt: {
          gte: new Date(periodStart),
          lte: new Date(periodEnd),
        }
      }
    })

    if (unpaidEarnings.length === 0) {
      return NextResponse.json(
        { error: "No unpaid earnings found for this period" },
        { status: 400 }
      )
    }

    // Calculate total payout amount
    const totalAmount = unpaidEarnings.reduce(
      (sum, earning) => sum + Number(earning.authorEarning),
      0
    )

    // Check minimum payout amount
    if (totalAmount < REVENUE_SHARE.MINIMUM_PAYOUT_AMOUNT) {
      return NextResponse.json(
        { 
          error: `Minimum payout amount is $${REVENUE_SHARE.MINIMUM_PAYOUT_AMOUNT}`,
          currentAmount: totalAmount
        },
        { status: 400 }
      )
    }

    // Create payout record
    const payout = await prisma.payout.create({
      data: {
        userId,
        amount: totalAmount,
        status: PayoutStatus.PENDING,
        periodStart: new Date(periodStart),
        periodEnd: new Date(periodEnd),
        description: `Payout for period ${periodStart} to ${periodEnd}`,
        scheduledAt: new Date(),
      }
    })

    // Update earnings to reference this payout
    await prisma.earning.updateMany({
      where: {
        id: { in: unpaidEarnings.map(e => e.id) }
      },
      data: {
        payoutId: payout.id,
        isPaidOut: true,
      }
    })

    // Process Stripe transfer
    try {
      const transfer = await stripe.transfers.create({
        amount: Math.round(totalAmount * 100), // Convert to cents
        currency: 'usd',
        destination: user.stripeAccountId,
        description: `Payout for ${user.name || user.email}`,
        metadata: {
          payoutId: payout.id,
          userId: user.id,
        }
      })

      // Update payout with Stripe transfer ID
      await prisma.payout.update({
        where: { id: payout.id },
        data: {
          stripeTransferId: transfer.id,
          status: PayoutStatus.PROCESSING,
          processedAt: new Date(),
        }
      })

      return NextResponse.json({
        message: "Payout initiated successfully",
        payout: {
          ...payout,
          stripeTransferId: transfer.id,
          status: PayoutStatus.PROCESSING,
        }
      })

    } catch (stripeError: any) {
      console.error("Stripe transfer error:", stripeError)

      // Update payout status to failed
      await prisma.payout.update({
        where: { id: payout.id },
        data: {
          status: PayoutStatus.FAILED,
          failureReason: stripeError.message,
        }
      })

      // Revert earnings payout status
      await prisma.earning.updateMany({
        where: {
          id: { in: unpaidEarnings.map(e => e.id) }
        },
        data: {
          payoutId: null,
          isPaidOut: false,
        }
      })

      return NextResponse.json(
        { error: "Failed to process payout", details: stripeError.message },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("Error creating payout:", error)
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    )
  }
}
