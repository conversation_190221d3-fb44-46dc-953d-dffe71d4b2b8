import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { stripe } from "@/lib/stripe"
import { UserRole } from "@prisma/client"

// POST /api/stripe/connect/account-link - Create account link for onboarding
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { type = 'account_onboarding' } = body

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user || !user.stripeAccountId) {
      return NextResponse.json(
        { error: "Stripe Connect account not found" },
        { status: 404 }
      )
    }

    // Create account link
    const accountLink = await stripe.accountLinks.create({
      account: user.stripeAccountId,
      refresh_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/payouts?refresh=true`,
      return_url: `${process.env.NEXT_PUBLIC_APP_URL}/dashboard/payouts?success=true`,
      type: type as 'account_onboarding' | 'account_update',
    })

    return NextResponse.json({
      url: accountLink.url,
    })

  } catch (error) {
    console.error("Error creating account link:", error)
    return NextResponse.json(
      { error: "Failed to create account link" },
      { status: 500 }
    )
  }
}
