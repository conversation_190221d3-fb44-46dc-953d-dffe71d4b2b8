import { NextRequest, NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { authOptions } from "@/lib/auth"
import { prisma } from "@/lib/db"
import { stripe } from "@/lib/stripe"
import { UserRole } from "@prisma/client"

// POST /api/stripe/connect - Create Stripe Connect account for author
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user) {
      return NextResponse.json({ error: "User not found" }, { status: 404 })
    }

    // Check if user already has a Stripe Connect account
    if (user.stripeAccountId) {
      // Get existing account status
      const account = await stripe.accounts.retrieve(user.stripeAccountId)
      
      return NextResponse.json({
        accountId: user.stripeAccountId,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
      })
    }

    // Create new Stripe Connect account
    const account = await stripe.accounts.create({
      type: 'express',
      country: 'US', // You might want to make this configurable
      email: user.email,
      metadata: {
        userId: user.id,
      },
      capabilities: {
        transfers: { requested: true },
      },
    })

    // Save Stripe account ID to user
    await prisma.user.update({
      where: { id: user.id },
      data: { stripeAccountId: account.id }
    })

    return NextResponse.json({
      accountId: account.id,
      detailsSubmitted: account.details_submitted,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
    })

  } catch (error) {
    console.error("Error creating Stripe Connect account:", error)
    return NextResponse.json(
      { error: "Failed to create Stripe Connect account" },
      { status: 500 }
    )
  }
}

// GET /api/stripe/connect - Get Stripe Connect account status
export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== UserRole.AUTHOR) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    })

    if (!user || !user.stripeAccountId) {
      return NextResponse.json({
        hasAccount: false,
        accountId: null,
        detailsSubmitted: false,
        chargesEnabled: false,
        payoutsEnabled: false,
      })
    }

    // Get account status from Stripe
    const account = await stripe.accounts.retrieve(user.stripeAccountId)

    return NextResponse.json({
      hasAccount: true,
      accountId: user.stripeAccountId,
      detailsSubmitted: account.details_submitted,
      chargesEnabled: account.charges_enabled,
      payoutsEnabled: account.payouts_enabled,
      requirements: account.requirements,
    })

  } catch (error) {
    console.error("Error fetching Stripe Connect account:", error)
    return NextResponse.json(
      { error: "Failed to fetch account status" },
      { status: 500 }
    )
  }
}
