"use client"

import { useSession } from "next-auth/react"
import { AuthGuard } from "@/components/auth/auth-guard"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent } from "@/components/ui/card"
import { EarningsDashboard } from "@/components/dashboard/earnings-dashboard"
import { PayoutDashboard } from "@/components/dashboard/payout-dashboard"
import { SubscriptionManager } from "@/components/subscription/subscription-manager"
import { DollarSign, CreditCard, Crown } from "lucide-react"

export default function MonetizationDashboard() {
  const { data: session } = useSession()

  return (
    <AuthGuard requiredRole="AUTHOR">
      <div className="container mx-auto py-8 space-y-8">
        {/* Header */}
        <div className="space-y-2">
          <h1 className="text-4xl font-bold">Monetization Dashboard</h1>
          <p className="text-xl text-muted-foreground">
            Manage your earnings, payouts, and subscription settings
          </p>
        </div>

        {/* Dashboard Tabs */}
        <Tabs defaultValue="earnings" className="space-y-6">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="earnings" className="flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Earnings
            </TabsTrigger>
            <TabsTrigger value="payouts" className="flex items-center gap-2">
              <CreditCard className="h-4 w-4" />
              Payouts
            </TabsTrigger>
            <TabsTrigger value="subscription" className="flex items-center gap-2">
              <Crown className="h-4 w-4" />
              Subscription
            </TabsTrigger>
          </TabsList>

          <TabsContent value="earnings" className="space-y-6">
            <EarningsDashboard />
          </TabsContent>

          <TabsContent value="payouts" className="space-y-6">
            <PayoutDashboard />
          </TabsContent>

          <TabsContent value="subscription" className="space-y-6">
            <div className="space-y-6">
              <div>
                <h2 className="text-2xl font-bold mb-2">Subscription Management</h2>
                <p className="text-muted-foreground">
                  Manage your subscription to access premium features and content
                </p>
              </div>
              <SubscriptionManager />
            </div>
          </TabsContent>
        </Tabs>

        {/* Quick Stats Footer */}
        <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <h3 className="font-semibold text-lg">💡 Monetization Tips</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div className="space-y-1">
                  <p className="font-medium">Create Premium Content</p>
                  <p className="text-muted-foreground">
                    Mark your best novels and chapters as premium to earn from subscriptions
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="font-medium">Engage Your Audience</p>
                  <p className="text-muted-foreground">
                    Regular updates and quality content encourage tips and subscriptions
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="font-medium">Set Up Payouts</p>
                  <p className="text-muted-foreground">
                    Complete your Stripe Connect setup to receive monthly payouts
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AuthGuard>
  )
}
