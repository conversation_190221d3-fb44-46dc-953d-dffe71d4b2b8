"use client"

import { useState } from "react"
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { PricingPlans } from "@/components/subscription/pricing-plans"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { useCreateSubscriptionMutation } from "@/store/api/subscriptionsApi"
import { getStripe } from "@/lib/stripe"
import { SubscriptionTier } from "@prisma/client"
import { Crown, Star, Zap, ArrowLeft } from "lucide-react"
import Link from "next/link"

export default function PricingPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const { toast } = useToast()
  const [isLoading, setIsLoading] = useState(false)
  const [selectedPlan, setSelectedPlan] = useState<{
    tier: SubscriptionTier
    isYearly: boolean
  } | null>(null)

  const [createSubscription] = useCreateSubscriptionMutation()

  const handleSelectPlan = async (tier: SubscriptionTier, isYearly: boolean) => {
    if (!session?.user) {
      router.push('/auth/signin?callbackUrl=/pricing')
      return
    }

    setSelectedPlan({ tier, isYearly })
    setIsLoading(true)

    try {
      // For demo purposes, we'll simulate payment method creation
      // In a real app, you'd collect payment method details first
      const stripe = await getStripe()
      if (!stripe) {
        throw new Error('Stripe not loaded')
      }

      // Create a test payment method (in production, collect from user)
      const { paymentMethod, error: pmError } = await stripe.createPaymentMethod({
        type: 'card',
        card: {
          number: '****************',
          exp_month: 12,
          exp_year: 2025,
          cvc: '123',
        },
      })

      if (pmError || !paymentMethod) {
        throw new Error(pmError?.message || 'Failed to create payment method')
      }

      // Create subscription
      const result = await createSubscription({
        tier,
        paymentMethodId: paymentMethod.id,
        isYearly,
      }).unwrap()

      if (result.clientSecret) {
        // Confirm payment if needed
        const { error: confirmError } = await stripe.confirmCardPayment(result.clientSecret)
        
        if (confirmError) {
          throw new Error(confirmError.message)
        }
      }

      toast({
        title: "Subscription Created!",
        description: `Welcome to ${tier}! Your subscription is now active.`,
      })

      router.push('/dashboard/monetization?tab=subscription')

    } catch (error: any) {
      console.error('Subscription error:', error)
      toast({
        title: "Subscription Failed",
        description: error.message || "Failed to create subscription. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
      setSelectedPlan(null)
    }
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2 mb-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Home
            </Link>
          </Button>
        </div>
        
        <h1 className="text-4xl font-bold">Choose Your Plan</h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Unlock premium content and support your favorite authors with our flexible subscription plans
        </p>
      </div>

      {/* Features Comparison */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="text-center">
          <CardHeader>
            <Star className="h-8 w-8 mx-auto text-gray-500" />
            <CardTitle>Free Readers</CardTitle>
            <CardDescription>
              Perfect for casual readers who want to explore
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2">
              <li>✓ Access to free novels</li>
              <li>✓ Basic reading progress</li>
              <li>✓ Library management</li>
              <li>✗ Premium content</li>
              <li>✗ Ad-free experience</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="text-center border-blue-200 bg-blue-50">
          <CardHeader>
            <Crown className="h-8 w-8 mx-auto text-blue-500" />
            <CardTitle>Premium Readers</CardTitle>
            <CardDescription>
              For avid readers who want the full experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2">
              <li>✓ Everything in Free</li>
              <li>✓ Access to premium novels</li>
              <li>✓ Ad-free reading</li>
              <li>✓ Early chapter access</li>
              <li>✓ Support authors</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="text-center border-purple-200 bg-purple-50">
          <CardHeader>
            <Zap className="h-8 w-8 mx-auto text-purple-500" />
            <CardTitle>Premium Plus</CardTitle>
            <CardDescription>
              For super fans who want exclusive access
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2">
              <li>✓ Everything in Premium</li>
              <li>✓ Exclusive content</li>
              <li>✓ Direct author messaging</li>
              <li>✓ Priority support</li>
              <li>✓ Beta features</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Pricing Plans */}
      <PricingPlans onSelectPlan={handleSelectPlan} />

      {/* FAQ Section */}
      <div className="space-y-6">
        <h2 className="text-2xl font-bold text-center">Frequently Asked Questions</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Can I cancel anytime?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Yes! You can cancel your subscription at any time. You'll continue to have access 
                until the end of your current billing period.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">What payment methods do you accept?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                We accept all major credit cards (Visa, Mastercard, American Express) 
                and other payment methods through Stripe.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">How do authors get paid?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Authors receive 70% of subscription revenue from their premium content. 
                Payouts are processed monthly via Stripe Connect.
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Is there a free trial?</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Yes! All paid plans include a 7-day free trial. You won't be charged 
                until the trial period ends.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="p-6">
            <div className="flex items-center space-x-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <div>
                <p className="font-medium">Processing your subscription...</p>
                <p className="text-sm text-muted-foreground">
                  Setting up {selectedPlan?.tier} {selectedPlan?.isYearly ? 'Yearly' : 'Monthly'}
                </p>
              </div>
            </div>
          </Card>
        </div>
      )}
    </div>
  )
}
