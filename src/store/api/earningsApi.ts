import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Earning, Payout, EarningType } from '@prisma/client'

export interface EarningWithPayout extends Earning {
  payout?: Pick<Payout, 'id' | 'status' | 'processedAt'> | null
}

export interface EarningsResponse {
  data: EarningWithPayout[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface EarningsAnalytics {
  totalEarnings: number
  monthlyEarnings: number
  pendingPayouts: number
  completedPayouts: number
  earningsByType: Record<EarningType, number>
  earningsByMonth: Array<{
    month: string
    amount: number
  }>
}

export interface EarningsQueryParams {
  page?: number
  limit?: number
  type?: EarningType
  startDate?: string
  endDate?: string
}

export const earningsApi = createApi({
  reducerPath: 'earningsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api/earnings',
  }),
  tagTypes: ['Earnings', 'EarningsAnalytics'],
  endpoints: (builder) => ({
    // Get earnings with pagination and filtering
    getEarnings: builder.query<EarningsResponse, EarningsQueryParams>({
      query: (params = {}) => ({
        url: '',
        params: {
          page: params.page?.toString(),
          limit: params.limit?.toString(),
          type: params.type,
          startDate: params.startDate,
          endDate: params.endDate,
        },
      }),
      providesTags: ['Earnings'],
    }),

    // Get earnings analytics
    getEarningsAnalytics: builder.query<EarningsAnalytics, void>({
      query: () => '/analytics',
      providesTags: ['EarningsAnalytics'],
    }),
  }),
})

export const {
  useGetEarningsQuery,
  useGetEarningsAnalyticsQuery,
} = earningsApi
