import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react'
import type { Payout, PayoutStatus, Earning } from '@prisma/client'

export interface PayoutWithEarnings extends Payout {
  earnings: Pick<Earning, 'id' | 'type' | 'amount' | 'authorEarning' | 'createdAt'>[]
}

export interface PayoutsResponse {
  data: PayoutWithEarnings[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface PayoutsQueryParams {
  page?: number
  limit?: number
  status?: PayoutStatus
}

export interface StripeConnectAccount {
  hasAccount: boolean
  accountId: string | null
  detailsSubmitted: boolean
  chargesEnabled: boolean
  payoutsEnabled: boolean
  requirements?: any
}

export interface CreateAccountLinkRequest {
  type?: 'account_onboarding' | 'account_update'
}

export interface CreateAccountLinkResponse {
  url: string
}

export const payoutsApi = createApi({
  reducerPath: 'payoutsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: '/api',
  }),
  tagTypes: ['Payouts', 'StripeConnect'],
  endpoints: (builder) => ({
    // Get payouts with pagination and filtering
    getPayouts: builder.query<PayoutsResponse, PayoutsQueryParams>({
      query: (params = {}) => ({
        url: '/payouts',
        params: {
          page: params.page?.toString(),
          limit: params.limit?.toString(),
          status: params.status,
        },
      }),
      providesTags: ['Payouts'],
    }),

    // Get Stripe Connect account status
    getStripeConnectAccount: builder.query<StripeConnectAccount, void>({
      query: () => '/stripe/connect',
      providesTags: ['StripeConnect'],
    }),

    // Create Stripe Connect account
    createStripeConnectAccount: builder.mutation<StripeConnectAccount, void>({
      query: () => ({
        url: '/stripe/connect',
        method: 'POST',
      }),
      invalidatesTags: ['StripeConnect'],
    }),

    // Create account link for onboarding
    createAccountLink: builder.mutation<CreateAccountLinkResponse, CreateAccountLinkRequest>({
      query: (data) => ({
        url: '/stripe/connect/account-link',
        method: 'POST',
        body: data,
      }),
    }),
  }),
})

export const {
  useGetPayoutsQuery,
  useGetStripeConnectAccountQuery,
  useCreateStripeConnectAccountMutation,
  useCreateAccountLinkMutation,
} = payoutsApi
