{"name": "black-blog", "version": "1.0.0", "description": "A modern novel writing and reading platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "type-check": "tsc --noEmit"}, "dependencies": {"@google-cloud/storage": "^7.16.0", "@hookform/resolvers": "^3.6.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.15.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.0", "@radix-ui/react-dropdown-menu": "^2.1.0", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.0", "@reduxjs/toolkit": "^2.2.0", "@stripe/stripe-js": "^7.4.0", "@supabase/supabase-js": "^2.43.0", "@tailwindcss/typography": "^0.5.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.0", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "date-fns": "^4.1.0", "lucide-react": "^0.395.0", "next": "^14.2.0", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "postcss": "^8.4.0", "prisma": "^5.15.0", "react": "^18.3.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.3.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.52.0", "react-redux": "^9.1.0", "stripe": "^18.3.0", "tailwind-merge": "^2.3.0", "tailwindcss": "^3.4.0", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "zod": "^3.23.0"}, "devDependencies": {"@types/node": "^20.19.4", "@types/react": "^18.3.0", "@types/react-dom": "^18.3.0", "@typescript-eslint/eslint-plugin": "^7.13.0", "@typescript-eslint/parser": "^7.13.0", "eslint": "^8.57.0", "eslint-config-next": "^14.2.0", "typescript": "^5.5.0"}, "keywords": ["nextjs", "novel", "writing", "reading", "platform"], "author": "", "license": "MIT"}