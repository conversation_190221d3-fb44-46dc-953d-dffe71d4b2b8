"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/webhooks/stripe/route";
exports.ids = ["app/api/webhooks/stripe/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "./action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "./request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "child_process":
/*!********************************!*\
  !*** external "child_process" ***!
  \********************************/
/***/ ((module) => {

module.exports = require("child_process");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwebhooks%2Fstripe%2Froute&page=%2Fapi%2Fwebhooks%2Fstripe%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fstripe%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwebhooks%2Fstripe%2Froute&page=%2Fapi%2Fwebhooks%2Fstripe%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fstripe%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_webhooks_stripe_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/webhooks/stripe/route.ts */ \"(rsc)/./src/app/api/webhooks/stripe/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/webhooks/stripe/route\",\n        pathname: \"/api/webhooks/stripe\",\n        filename: \"route\",\n        bundlePath: \"app/api/webhooks/stripe/route\"\n    },\n    resolvedPagePath: \"/Users/<USER>/Desktop/adc-platform/services/content/black-blog/src/app/api/webhooks/stripe/route.ts\",\n    nextConfigOutput,\n    userland: _Users_weerawat_Desktop_adc_platform_services_content_black_blog_src_app_api_webhooks_stripe_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/webhooks/stripe/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwebhooks%2Fstripe%2Froute&page=%2Fapi%2Fwebhooks%2Fstripe%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fstripe%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/webhooks/stripe/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/webhooks/stripe/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   runtime: () => (/* binding */ runtime)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n/* harmony import */ var _lib_db__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/db */ \"(rsc)/./src/lib/db.ts\");\n/* harmony import */ var _lib_stripe__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/stripe */ \"(rsc)/./src/lib/stripe.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Disable body parsing for webhook\nconst runtime = \"nodejs\";\nasync function POST(request) {\n    try {\n        const body = await request.text();\n        const signature = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.headers)().get(\"stripe-signature\");\n        if (!signature) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing stripe signature\"\n            }, {\n                status: 400\n            });\n        }\n        // Verify webhook signature\n        const event = (0,_lib_stripe__WEBPACK_IMPORTED_MODULE_3__.verifyStripeWebhook)(body, signature);\n        console.log(`Processing Stripe webhook: ${event.type}`);\n        switch(event.type){\n            case \"customer.subscription.created\":\n            case \"customer.subscription.updated\":\n                await handleSubscriptionUpdate(event.data.object);\n                break;\n            case \"customer.subscription.deleted\":\n                await handleSubscriptionDeleted(event.data.object);\n                break;\n            case \"invoice.payment_succeeded\":\n                await handlePaymentSucceeded(event.data.object);\n                break;\n            case \"invoice.payment_failed\":\n                await handlePaymentFailed(event.data.object);\n                break;\n            default:\n                console.log(`Unhandled event type: ${event.type}`);\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            received: true\n        });\n    } catch (error) {\n        console.error(\"Webhook error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Webhook handler failed\"\n        }, {\n            status: 400\n        });\n    }\n}\nasync function handleSubscriptionUpdate(stripeSubscription) {\n    try {\n        const subscription = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.subscription.findFirst({\n            where: {\n                stripeSubscriptionId: stripeSubscription.id\n            }\n        });\n        if (!subscription) {\n            console.error(`Subscription not found: ${stripeSubscription.id}`);\n            return;\n        }\n        // Map Stripe status to our enum\n        let status;\n        switch(stripeSubscription.status){\n            case \"active\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.ACTIVE;\n                break;\n            case \"canceled\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.CANCELED;\n                break;\n            case \"past_due\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.PAST_DUE;\n                break;\n            case \"unpaid\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.UNPAID;\n                break;\n            case \"incomplete\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.INCOMPLETE;\n                break;\n            case \"incomplete_expired\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.INCOMPLETE_EXPIRED;\n                break;\n            case \"trialing\":\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.TRIALING;\n                break;\n            default:\n                status = _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.ACTIVE;\n        }\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.subscription.update({\n            where: {\n                id: subscription.id\n            },\n            data: {\n                status,\n                currentPeriodStart: new Date(stripeSubscription.current_period_start * 1000),\n                currentPeriodEnd: new Date(stripeSubscription.current_period_end * 1000),\n                cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end,\n                trialStart: stripeSubscription.trial_start ? new Date(stripeSubscription.trial_start * 1000) : null,\n                trialEnd: stripeSubscription.trial_end ? new Date(stripeSubscription.trial_end * 1000) : null\n            }\n        });\n        console.log(`Updated subscription ${subscription.id} to status ${status}`);\n    } catch (error) {\n        console.error(\"Error handling subscription update:\", error);\n    }\n}\nasync function handleSubscriptionDeleted(stripeSubscription) {\n    try {\n        const subscription = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.subscription.findFirst({\n            where: {\n                stripeSubscriptionId: stripeSubscription.id\n            }\n        });\n        if (!subscription) {\n            console.error(`Subscription not found: ${stripeSubscription.id}`);\n            return;\n        }\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.subscription.update({\n            where: {\n                id: subscription.id\n            },\n            data: {\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.SubscriptionStatus.CANCELED,\n                canceledAt: new Date()\n            }\n        });\n        console.log(`Canceled subscription ${subscription.id}`);\n    } catch (error) {\n        console.error(\"Error handling subscription deletion:\", error);\n    }\n}\nasync function handlePaymentSucceeded(invoice) {\n    try {\n        if (!invoice.subscription) return;\n        const subscription = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.subscription.findFirst({\n            where: {\n                stripeSubscriptionId: invoice.subscription\n            },\n            include: {\n                user: true\n            }\n        });\n        if (!subscription) {\n            console.error(`Subscription not found: ${invoice.subscription}`);\n            return;\n        }\n        // Create payment record\n        const payment = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.payment.create({\n            data: {\n                userId: subscription.userId,\n                subscriptionId: subscription.id,\n                stripePaymentId: invoice.payment_intent,\n                amount: invoice.amount_paid / 100,\n                currency: invoice.currency,\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.PaymentStatus.COMPLETED,\n                description: `Subscription payment for ${subscription.tier}`\n            }\n        });\n        console.log(`Created payment record ${payment.id} for subscription ${subscription.id}`);\n    } catch (error) {\n        console.error(\"Error handling payment succeeded:\", error);\n    }\n}\nasync function handlePaymentFailed(invoice) {\n    try {\n        if (!invoice.subscription) return;\n        const subscription = await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.subscription.findFirst({\n            where: {\n                stripeSubscriptionId: invoice.subscription\n            }\n        });\n        if (!subscription) {\n            console.error(`Subscription not found: ${invoice.subscription}`);\n            return;\n        }\n        // Create failed payment record\n        await _lib_db__WEBPACK_IMPORTED_MODULE_2__.prisma.payment.create({\n            data: {\n                userId: subscription.userId,\n                subscriptionId: subscription.id,\n                stripePaymentId: invoice.payment_intent,\n                amount: invoice.amount_due / 100,\n                currency: invoice.currency,\n                status: _prisma_client__WEBPACK_IMPORTED_MODULE_4__.PaymentStatus.FAILED,\n                description: `Failed subscription payment for ${subscription.tier}`,\n                failureReason: \"Payment failed\"\n            }\n        });\n        console.log(`Created failed payment record for subscription ${subscription.id}`);\n    } catch (error) {\n        console.error(\"Error handling payment failed:\", error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS93ZWJob29rcy9zdHJpcGUvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBdUQ7QUFDakI7QUFDTDtBQUN3QztBQUNNO0FBRy9FLG1DQUFtQztBQUM1QixNQUFNTSxVQUFVLFNBQVE7QUFFeEIsZUFBZUMsS0FBS0MsT0FBb0I7SUFDN0MsSUFBSTtRQUNGLE1BQU1DLE9BQU8sTUFBTUQsUUFBUUUsSUFBSTtRQUMvQixNQUFNQyxZQUFZVixxREFBT0EsR0FBR1csR0FBRyxDQUFDO1FBRWhDLElBQUksQ0FBQ0QsV0FBVztZQUNkLE9BQU9YLHFEQUFZQSxDQUFDYSxJQUFJLENBQ3RCO2dCQUFFQyxPQUFPO1lBQTJCLEdBQ3BDO2dCQUFFQyxRQUFRO1lBQUk7UUFFbEI7UUFFQSwyQkFBMkI7UUFDM0IsTUFBTUMsUUFBUWIsZ0VBQW1CQSxDQUFDTSxNQUFNRTtRQUV4Q00sUUFBUUMsR0FBRyxDQUFDLENBQUMsMkJBQTJCLEVBQUVGLE1BQU1HLElBQUksQ0FBQyxDQUFDO1FBRXRELE9BQVFILE1BQU1HLElBQUk7WUFDaEIsS0FBSztZQUNMLEtBQUs7Z0JBQ0gsTUFBTUMseUJBQXlCSixNQUFNSyxJQUFJLENBQUNDLE1BQU07Z0JBQ2hEO1lBRUYsS0FBSztnQkFDSCxNQUFNQywwQkFBMEJQLE1BQU1LLElBQUksQ0FBQ0MsTUFBTTtnQkFDakQ7WUFFRixLQUFLO2dCQUNILE1BQU1FLHVCQUF1QlIsTUFBTUssSUFBSSxDQUFDQyxNQUFNO2dCQUM5QztZQUVGLEtBQUs7Z0JBQ0gsTUFBTUcsb0JBQW9CVCxNQUFNSyxJQUFJLENBQUNDLE1BQU07Z0JBQzNDO1lBRUY7Z0JBQ0VMLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFRixNQUFNRyxJQUFJLENBQUMsQ0FBQztRQUNyRDtRQUVBLE9BQU9uQixxREFBWUEsQ0FBQ2EsSUFBSSxDQUFDO1lBQUVhLFVBQVU7UUFBSztJQUM1QyxFQUFFLE9BQU9aLE9BQU87UUFDZEcsUUFBUUgsS0FBSyxDQUFDLGtCQUFrQkE7UUFDaEMsT0FBT2QscURBQVlBLENBQUNhLElBQUksQ0FDdEI7WUFBRUMsT0FBTztRQUF5QixHQUNsQztZQUFFQyxRQUFRO1FBQUk7SUFFbEI7QUFDRjtBQUVBLGVBQWVLLHlCQUF5Qk8sa0JBQXVDO0lBQzdFLElBQUk7UUFDRixNQUFNQyxlQUFlLE1BQU0xQiwyQ0FBTUEsQ0FBQzBCLFlBQVksQ0FBQ0MsU0FBUyxDQUFDO1lBQ3ZEQyxPQUFPO2dCQUNMQyxzQkFBc0JKLG1CQUFtQkssRUFBRTtZQUM3QztRQUNGO1FBRUEsSUFBSSxDQUFDSixjQUFjO1lBQ2pCWCxRQUFRSCxLQUFLLENBQUMsQ0FBQyx3QkFBd0IsRUFBRWEsbUJBQW1CSyxFQUFFLENBQUMsQ0FBQztZQUNoRTtRQUNGO1FBRUEsZ0NBQWdDO1FBQ2hDLElBQUlqQjtRQUNKLE9BQVFZLG1CQUFtQlosTUFBTTtZQUMvQixLQUFLO2dCQUNIQSxTQUFTWCw4REFBa0JBLENBQUM2QixNQUFNO2dCQUNsQztZQUNGLEtBQUs7Z0JBQ0hsQixTQUFTWCw4REFBa0JBLENBQUM4QixRQUFRO2dCQUNwQztZQUNGLEtBQUs7Z0JBQ0huQixTQUFTWCw4REFBa0JBLENBQUMrQixRQUFRO2dCQUNwQztZQUNGLEtBQUs7Z0JBQ0hwQixTQUFTWCw4REFBa0JBLENBQUNnQyxNQUFNO2dCQUNsQztZQUNGLEtBQUs7Z0JBQ0hyQixTQUFTWCw4REFBa0JBLENBQUNpQyxVQUFVO2dCQUN0QztZQUNGLEtBQUs7Z0JBQ0h0QixTQUFTWCw4REFBa0JBLENBQUNrQyxrQkFBa0I7Z0JBQzlDO1lBQ0YsS0FBSztnQkFDSHZCLFNBQVNYLDhEQUFrQkEsQ0FBQ21DLFFBQVE7Z0JBQ3BDO1lBQ0Y7Z0JBQ0V4QixTQUFTWCw4REFBa0JBLENBQUM2QixNQUFNO1FBQ3RDO1FBRUEsTUFBTS9CLDJDQUFNQSxDQUFDMEIsWUFBWSxDQUFDWSxNQUFNLENBQUM7WUFDL0JWLE9BQU87Z0JBQUVFLElBQUlKLGFBQWFJLEVBQUU7WUFBQztZQUM3QlgsTUFBTTtnQkFDSk47Z0JBQ0EwQixvQkFBb0IsSUFBSUMsS0FBS2YsbUJBQW1CZ0Isb0JBQW9CLEdBQUc7Z0JBQ3ZFQyxrQkFBa0IsSUFBSUYsS0FBS2YsbUJBQW1Ca0Isa0JBQWtCLEdBQUc7Z0JBQ25FQyxtQkFBbUJuQixtQkFBbUJvQixvQkFBb0I7Z0JBQzFEQyxZQUFZckIsbUJBQW1Cc0IsV0FBVyxHQUFHLElBQUlQLEtBQUtmLG1CQUFtQnNCLFdBQVcsR0FBRyxRQUFRO2dCQUMvRkMsVUFBVXZCLG1CQUFtQndCLFNBQVMsR0FBRyxJQUFJVCxLQUFLZixtQkFBbUJ3QixTQUFTLEdBQUcsUUFBUTtZQUMzRjtRQUNGO1FBRUFsQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyxxQkFBcUIsRUFBRVUsYUFBYUksRUFBRSxDQUFDLFdBQVcsRUFBRWpCLE9BQU8sQ0FBQztJQUMzRSxFQUFFLE9BQU9ELE9BQU87UUFDZEcsUUFBUUgsS0FBSyxDQUFDLHVDQUF1Q0E7SUFDdkQ7QUFDRjtBQUVBLGVBQWVTLDBCQUEwQkksa0JBQXVDO0lBQzlFLElBQUk7UUFDRixNQUFNQyxlQUFlLE1BQU0xQiwyQ0FBTUEsQ0FBQzBCLFlBQVksQ0FBQ0MsU0FBUyxDQUFDO1lBQ3ZEQyxPQUFPO2dCQUNMQyxzQkFBc0JKLG1CQUFtQkssRUFBRTtZQUM3QztRQUNGO1FBRUEsSUFBSSxDQUFDSixjQUFjO1lBQ2pCWCxRQUFRSCxLQUFLLENBQUMsQ0FBQyx3QkFBd0IsRUFBRWEsbUJBQW1CSyxFQUFFLENBQUMsQ0FBQztZQUNoRTtRQUNGO1FBRUEsTUFBTTlCLDJDQUFNQSxDQUFDMEIsWUFBWSxDQUFDWSxNQUFNLENBQUM7WUFDL0JWLE9BQU87Z0JBQUVFLElBQUlKLGFBQWFJLEVBQUU7WUFBQztZQUM3QlgsTUFBTTtnQkFDSk4sUUFBUVgsOERBQWtCQSxDQUFDOEIsUUFBUTtnQkFDbkNrQixZQUFZLElBQUlWO1lBQ2xCO1FBQ0Y7UUFFQXpCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNCQUFzQixFQUFFVSxhQUFhSSxFQUFFLENBQUMsQ0FBQztJQUN4RCxFQUFFLE9BQU9sQixPQUFPO1FBQ2RHLFFBQVFILEtBQUssQ0FBQyx5Q0FBeUNBO0lBQ3pEO0FBQ0Y7QUFFQSxlQUFlVSx1QkFBdUI2QixPQUF1QjtJQUMzRCxJQUFJO1FBQ0YsSUFBSSxDQUFDQSxRQUFRekIsWUFBWSxFQUFFO1FBRTNCLE1BQU1BLGVBQWUsTUFBTTFCLDJDQUFNQSxDQUFDMEIsWUFBWSxDQUFDQyxTQUFTLENBQUM7WUFDdkRDLE9BQU87Z0JBQ0xDLHNCQUFzQnNCLFFBQVF6QixZQUFZO1lBQzVDO1lBQ0EwQixTQUFTO2dCQUNQQyxNQUFNO1lBQ1I7UUFDRjtRQUVBLElBQUksQ0FBQzNCLGNBQWM7WUFDakJYLFFBQVFILEtBQUssQ0FBQyxDQUFDLHdCQUF3QixFQUFFdUMsUUFBUXpCLFlBQVksQ0FBQyxDQUFDO1lBQy9EO1FBQ0Y7UUFFQSx3QkFBd0I7UUFDeEIsTUFBTTRCLFVBQVUsTUFBTXRELDJDQUFNQSxDQUFDc0QsT0FBTyxDQUFDQyxNQUFNLENBQUM7WUFDMUNwQyxNQUFNO2dCQUNKcUMsUUFBUTlCLGFBQWE4QixNQUFNO2dCQUMzQkMsZ0JBQWdCL0IsYUFBYUksRUFBRTtnQkFDL0I0QixpQkFBaUJQLFFBQVFRLGNBQWM7Z0JBQ3ZDQyxRQUFRVCxRQUFRVSxXQUFXLEdBQUc7Z0JBQzlCQyxVQUFVWCxRQUFRVyxRQUFRO2dCQUMxQmpELFFBQVFWLHlEQUFhQSxDQUFDNEQsU0FBUztnQkFDL0JDLGFBQWEsQ0FBQyx5QkFBeUIsRUFBRXRDLGFBQWF1QyxJQUFJLENBQUMsQ0FBQztZQUM5RDtRQUNGO1FBRUFsRCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx1QkFBdUIsRUFBRXNDLFFBQVF4QixFQUFFLENBQUMsa0JBQWtCLEVBQUVKLGFBQWFJLEVBQUUsQ0FBQyxDQUFDO0lBQ3hGLEVBQUUsT0FBT2xCLE9BQU87UUFDZEcsUUFBUUgsS0FBSyxDQUFDLHFDQUFxQ0E7SUFDckQ7QUFDRjtBQUVBLGVBQWVXLG9CQUFvQjRCLE9BQXVCO0lBQ3hELElBQUk7UUFDRixJQUFJLENBQUNBLFFBQVF6QixZQUFZLEVBQUU7UUFFM0IsTUFBTUEsZUFBZSxNQUFNMUIsMkNBQU1BLENBQUMwQixZQUFZLENBQUNDLFNBQVMsQ0FBQztZQUN2REMsT0FBTztnQkFDTEMsc0JBQXNCc0IsUUFBUXpCLFlBQVk7WUFDNUM7UUFDRjtRQUVBLElBQUksQ0FBQ0EsY0FBYztZQUNqQlgsUUFBUUgsS0FBSyxDQUFDLENBQUMsd0JBQXdCLEVBQUV1QyxRQUFRekIsWUFBWSxDQUFDLENBQUM7WUFDL0Q7UUFDRjtRQUVBLCtCQUErQjtRQUMvQixNQUFNMUIsMkNBQU1BLENBQUNzRCxPQUFPLENBQUNDLE1BQU0sQ0FBQztZQUMxQnBDLE1BQU07Z0JBQ0pxQyxRQUFROUIsYUFBYThCLE1BQU07Z0JBQzNCQyxnQkFBZ0IvQixhQUFhSSxFQUFFO2dCQUMvQjRCLGlCQUFpQlAsUUFBUVEsY0FBYztnQkFDdkNDLFFBQVFULFFBQVFlLFVBQVUsR0FBRztnQkFDN0JKLFVBQVVYLFFBQVFXLFFBQVE7Z0JBQzFCakQsUUFBUVYseURBQWFBLENBQUNnRSxNQUFNO2dCQUM1QkgsYUFBYSxDQUFDLGdDQUFnQyxFQUFFdEMsYUFBYXVDLElBQUksQ0FBQyxDQUFDO2dCQUNuRUcsZUFBZTtZQUNqQjtRQUNGO1FBRUFyRCxRQUFRQyxHQUFHLENBQUMsQ0FBQywrQ0FBK0MsRUFBRVUsYUFBYUksRUFBRSxDQUFDLENBQUM7SUFDakYsRUFBRSxPQUFPbEIsT0FBTztRQUNkRyxRQUFRSCxLQUFLLENBQUMsa0NBQWtDQTtJQUNsRDtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYmxhY2stYmxvZy8uL3NyYy9hcHAvYXBpL3dlYmhvb2tzL3N0cmlwZS9yb3V0ZS50cz9jNmY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IE5leHRSZXF1ZXN0LCBOZXh0UmVzcG9uc2UgfSBmcm9tIFwibmV4dC9zZXJ2ZXJcIlxuaW1wb3J0IHsgaGVhZGVycyB9IGZyb20gXCJuZXh0L2hlYWRlcnNcIlxuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIkAvbGliL2RiXCJcbmltcG9ydCB7IHZlcmlmeVN0cmlwZVdlYmhvb2ssIGNhbGN1bGF0ZVJldmVudWVTcGxpdCB9IGZyb20gXCJAL2xpYi9zdHJpcGVcIlxuaW1wb3J0IHsgU3Vic2NyaXB0aW9uU3RhdHVzLCBQYXltZW50U3RhdHVzLCBFYXJuaW5nVHlwZSB9IGZyb20gXCJAcHJpc21hL2NsaWVudFwiXG5pbXBvcnQgU3RyaXBlIGZyb20gXCJzdHJpcGVcIlxuXG4vLyBEaXNhYmxlIGJvZHkgcGFyc2luZyBmb3Igd2ViaG9va1xuZXhwb3J0IGNvbnN0IHJ1bnRpbWUgPSAnbm9kZWpzJ1xuXG5leHBvcnQgYXN5bmMgZnVuY3Rpb24gUE9TVChyZXF1ZXN0OiBOZXh0UmVxdWVzdCkge1xuICB0cnkge1xuICAgIGNvbnN0IGJvZHkgPSBhd2FpdCByZXF1ZXN0LnRleHQoKVxuICAgIGNvbnN0IHNpZ25hdHVyZSA9IGhlYWRlcnMoKS5nZXQoJ3N0cmlwZS1zaWduYXR1cmUnKVxuXG4gICAgaWYgKCFzaWduYXR1cmUpIHtcbiAgICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgICAgeyBlcnJvcjogXCJNaXNzaW5nIHN0cmlwZSBzaWduYXR1cmVcIiB9LFxuICAgICAgICB7IHN0YXR1czogNDAwIH1cbiAgICAgIClcbiAgICB9XG5cbiAgICAvLyBWZXJpZnkgd2ViaG9vayBzaWduYXR1cmVcbiAgICBjb25zdCBldmVudCA9IHZlcmlmeVN0cmlwZVdlYmhvb2soYm9keSwgc2lnbmF0dXJlKVxuXG4gICAgY29uc29sZS5sb2coYFByb2Nlc3NpbmcgU3RyaXBlIHdlYmhvb2s6ICR7ZXZlbnQudHlwZX1gKVxuXG4gICAgc3dpdGNoIChldmVudC50eXBlKSB7XG4gICAgICBjYXNlICdjdXN0b21lci5zdWJzY3JpcHRpb24uY3JlYXRlZCc6XG4gICAgICBjYXNlICdjdXN0b21lci5zdWJzY3JpcHRpb24udXBkYXRlZCc6XG4gICAgICAgIGF3YWl0IGhhbmRsZVN1YnNjcmlwdGlvblVwZGF0ZShldmVudC5kYXRhLm9iamVjdCBhcyBTdHJpcGUuU3Vic2NyaXB0aW9uKVxuICAgICAgICBicmVha1xuXG4gICAgICBjYXNlICdjdXN0b21lci5zdWJzY3JpcHRpb24uZGVsZXRlZCc6XG4gICAgICAgIGF3YWl0IGhhbmRsZVN1YnNjcmlwdGlvbkRlbGV0ZWQoZXZlbnQuZGF0YS5vYmplY3QgYXMgU3RyaXBlLlN1YnNjcmlwdGlvbilcbiAgICAgICAgYnJlYWtcblxuICAgICAgY2FzZSAnaW52b2ljZS5wYXltZW50X3N1Y2NlZWRlZCc6XG4gICAgICAgIGF3YWl0IGhhbmRsZVBheW1lbnRTdWNjZWVkZWQoZXZlbnQuZGF0YS5vYmplY3QgYXMgU3RyaXBlLkludm9pY2UpXG4gICAgICAgIGJyZWFrXG5cbiAgICAgIGNhc2UgJ2ludm9pY2UucGF5bWVudF9mYWlsZWQnOlxuICAgICAgICBhd2FpdCBoYW5kbGVQYXltZW50RmFpbGVkKGV2ZW50LmRhdGEub2JqZWN0IGFzIFN0cmlwZS5JbnZvaWNlKVxuICAgICAgICBicmVha1xuXG4gICAgICBkZWZhdWx0OlxuICAgICAgICBjb25zb2xlLmxvZyhgVW5oYW5kbGVkIGV2ZW50IHR5cGU6ICR7ZXZlbnQudHlwZX1gKVxuICAgIH1cblxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbih7IHJlY2VpdmVkOiB0cnVlIH0pXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIldlYmhvb2sgZXJyb3I6XCIsIGVycm9yKVxuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihcbiAgICAgIHsgZXJyb3I6IFwiV2ViaG9vayBoYW5kbGVyIGZhaWxlZFwiIH0sXG4gICAgICB7IHN0YXR1czogNDAwIH1cbiAgICApXG4gIH1cbn1cblxuYXN5bmMgZnVuY3Rpb24gaGFuZGxlU3Vic2NyaXB0aW9uVXBkYXRlKHN0cmlwZVN1YnNjcmlwdGlvbjogU3RyaXBlLlN1YnNjcmlwdGlvbikge1xuICB0cnkge1xuICAgIGNvbnN0IHN1YnNjcmlwdGlvbiA9IGF3YWl0IHByaXNtYS5zdWJzY3JpcHRpb24uZmluZEZpcnN0KHtcbiAgICAgIHdoZXJlOiB7XG4gICAgICAgIHN0cmlwZVN1YnNjcmlwdGlvbklkOiBzdHJpcGVTdWJzY3JpcHRpb24uaWQsXG4gICAgICB9LFxuICAgIH0pXG5cbiAgICBpZiAoIXN1YnNjcmlwdGlvbikge1xuICAgICAgY29uc29sZS5lcnJvcihgU3Vic2NyaXB0aW9uIG5vdCBmb3VuZDogJHtzdHJpcGVTdWJzY3JpcHRpb24uaWR9YClcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIE1hcCBTdHJpcGUgc3RhdHVzIHRvIG91ciBlbnVtXG4gICAgbGV0IHN0YXR1czogU3Vic2NyaXB0aW9uU3RhdHVzXG4gICAgc3dpdGNoIChzdHJpcGVTdWJzY3JpcHRpb24uc3RhdHVzKSB7XG4gICAgICBjYXNlICdhY3RpdmUnOlxuICAgICAgICBzdGF0dXMgPSBTdWJzY3JpcHRpb25TdGF0dXMuQUNUSVZFXG4gICAgICAgIGJyZWFrXG4gICAgICBjYXNlICdjYW5jZWxlZCc6XG4gICAgICAgIHN0YXR1cyA9IFN1YnNjcmlwdGlvblN0YXR1cy5DQU5DRUxFRFxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAncGFzdF9kdWUnOlxuICAgICAgICBzdGF0dXMgPSBTdWJzY3JpcHRpb25TdGF0dXMuUEFTVF9EVUVcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ3VucGFpZCc6XG4gICAgICAgIHN0YXR1cyA9IFN1YnNjcmlwdGlvblN0YXR1cy5VTlBBSURcbiAgICAgICAgYnJlYWtcbiAgICAgIGNhc2UgJ2luY29tcGxldGUnOlxuICAgICAgICBzdGF0dXMgPSBTdWJzY3JpcHRpb25TdGF0dXMuSU5DT01QTEVURVxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAnaW5jb21wbGV0ZV9leHBpcmVkJzpcbiAgICAgICAgc3RhdHVzID0gU3Vic2NyaXB0aW9uU3RhdHVzLklOQ09NUExFVEVfRVhQSVJFRFxuICAgICAgICBicmVha1xuICAgICAgY2FzZSAndHJpYWxpbmcnOlxuICAgICAgICBzdGF0dXMgPSBTdWJzY3JpcHRpb25TdGF0dXMuVFJJQUxJTkdcbiAgICAgICAgYnJlYWtcbiAgICAgIGRlZmF1bHQ6XG4gICAgICAgIHN0YXR1cyA9IFN1YnNjcmlwdGlvblN0YXR1cy5BQ1RJVkVcbiAgICB9XG5cbiAgICBhd2FpdCBwcmlzbWEuc3Vic2NyaXB0aW9uLnVwZGF0ZSh7XG4gICAgICB3aGVyZTogeyBpZDogc3Vic2NyaXB0aW9uLmlkIH0sXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHN0YXR1cyxcbiAgICAgICAgY3VycmVudFBlcmlvZFN0YXJ0OiBuZXcgRGF0ZShzdHJpcGVTdWJzY3JpcHRpb24uY3VycmVudF9wZXJpb2Rfc3RhcnQgKiAxMDAwKSxcbiAgICAgICAgY3VycmVudFBlcmlvZEVuZDogbmV3IERhdGUoc3RyaXBlU3Vic2NyaXB0aW9uLmN1cnJlbnRfcGVyaW9kX2VuZCAqIDEwMDApLFxuICAgICAgICBjYW5jZWxBdFBlcmlvZEVuZDogc3RyaXBlU3Vic2NyaXB0aW9uLmNhbmNlbF9hdF9wZXJpb2RfZW5kLFxuICAgICAgICB0cmlhbFN0YXJ0OiBzdHJpcGVTdWJzY3JpcHRpb24udHJpYWxfc3RhcnQgPyBuZXcgRGF0ZShzdHJpcGVTdWJzY3JpcHRpb24udHJpYWxfc3RhcnQgKiAxMDAwKSA6IG51bGwsXG4gICAgICAgIHRyaWFsRW5kOiBzdHJpcGVTdWJzY3JpcHRpb24udHJpYWxfZW5kID8gbmV3IERhdGUoc3RyaXBlU3Vic2NyaXB0aW9uLnRyaWFsX2VuZCAqIDEwMDApIDogbnVsbCxcbiAgICAgIH0sXG4gICAgfSlcblxuICAgIGNvbnNvbGUubG9nKGBVcGRhdGVkIHN1YnNjcmlwdGlvbiAke3N1YnNjcmlwdGlvbi5pZH0gdG8gc3RhdHVzICR7c3RhdHVzfWApXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGhhbmRsaW5nIHN1YnNjcmlwdGlvbiB1cGRhdGU6XCIsIGVycm9yKVxuICB9XG59XG5cbmFzeW5jIGZ1bmN0aW9uIGhhbmRsZVN1YnNjcmlwdGlvbkRlbGV0ZWQoc3RyaXBlU3Vic2NyaXB0aW9uOiBTdHJpcGUuU3Vic2NyaXB0aW9uKSB7XG4gIHRyeSB7XG4gICAgY29uc3Qgc3Vic2NyaXB0aW9uID0gYXdhaXQgcHJpc21hLnN1YnNjcmlwdGlvbi5maW5kRmlyc3Qoe1xuICAgICAgd2hlcmU6IHtcbiAgICAgICAgc3RyaXBlU3Vic2NyaXB0aW9uSWQ6IHN0cmlwZVN1YnNjcmlwdGlvbi5pZCxcbiAgICAgIH0sXG4gICAgfSlcblxuICAgIGlmICghc3Vic2NyaXB0aW9uKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBTdWJzY3JpcHRpb24gbm90IGZvdW5kOiAke3N0cmlwZVN1YnNjcmlwdGlvbi5pZH1gKVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgYXdhaXQgcHJpc21hLnN1YnNjcmlwdGlvbi51cGRhdGUoe1xuICAgICAgd2hlcmU6IHsgaWQ6IHN1YnNjcmlwdGlvbi5pZCB9LFxuICAgICAgZGF0YToge1xuICAgICAgICBzdGF0dXM6IFN1YnNjcmlwdGlvblN0YXR1cy5DQU5DRUxFRCxcbiAgICAgICAgY2FuY2VsZWRBdDogbmV3IERhdGUoKSxcbiAgICAgIH0sXG4gICAgfSlcblxuICAgIGNvbnNvbGUubG9nKGBDYW5jZWxlZCBzdWJzY3JpcHRpb24gJHtzdWJzY3JpcHRpb24uaWR9YClcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKFwiRXJyb3IgaGFuZGxpbmcgc3Vic2NyaXB0aW9uIGRlbGV0aW9uOlwiLCBlcnJvcilcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBoYW5kbGVQYXltZW50U3VjY2VlZGVkKGludm9pY2U6IFN0cmlwZS5JbnZvaWNlKSB7XG4gIHRyeSB7XG4gICAgaWYgKCFpbnZvaWNlLnN1YnNjcmlwdGlvbikgcmV0dXJuXG5cbiAgICBjb25zdCBzdWJzY3JpcHRpb24gPSBhd2FpdCBwcmlzbWEuc3Vic2NyaXB0aW9uLmZpbmRGaXJzdCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBzdHJpcGVTdWJzY3JpcHRpb25JZDogaW52b2ljZS5zdWJzY3JpcHRpb24gYXMgc3RyaW5nLFxuICAgICAgfSxcbiAgICAgIGluY2x1ZGU6IHtcbiAgICAgICAgdXNlcjogdHJ1ZSxcbiAgICAgIH0sXG4gICAgfSlcblxuICAgIGlmICghc3Vic2NyaXB0aW9uKSB7XG4gICAgICBjb25zb2xlLmVycm9yKGBTdWJzY3JpcHRpb24gbm90IGZvdW5kOiAke2ludm9pY2Uuc3Vic2NyaXB0aW9ufWApXG4gICAgICByZXR1cm5cbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgcGF5bWVudCByZWNvcmRcbiAgICBjb25zdCBwYXltZW50ID0gYXdhaXQgcHJpc21hLnBheW1lbnQuY3JlYXRlKHtcbiAgICAgIGRhdGE6IHtcbiAgICAgICAgdXNlcklkOiBzdWJzY3JpcHRpb24udXNlcklkLFxuICAgICAgICBzdWJzY3JpcHRpb25JZDogc3Vic2NyaXB0aW9uLmlkLFxuICAgICAgICBzdHJpcGVQYXltZW50SWQ6IGludm9pY2UucGF5bWVudF9pbnRlbnQgYXMgc3RyaW5nLFxuICAgICAgICBhbW91bnQ6IGludm9pY2UuYW1vdW50X3BhaWQgLyAxMDAsIC8vIENvbnZlcnQgZnJvbSBjZW50c1xuICAgICAgICBjdXJyZW5jeTogaW52b2ljZS5jdXJyZW5jeSxcbiAgICAgICAgc3RhdHVzOiBQYXltZW50U3RhdHVzLkNPTVBMRVRFRCxcbiAgICAgICAgZGVzY3JpcHRpb246IGBTdWJzY3JpcHRpb24gcGF5bWVudCBmb3IgJHtzdWJzY3JpcHRpb24udGllcn1gLFxuICAgICAgfSxcbiAgICB9KVxuXG4gICAgY29uc29sZS5sb2coYENyZWF0ZWQgcGF5bWVudCByZWNvcmQgJHtwYXltZW50LmlkfSBmb3Igc3Vic2NyaXB0aW9uICR7c3Vic2NyaXB0aW9uLmlkfWApXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgY29uc29sZS5lcnJvcihcIkVycm9yIGhhbmRsaW5nIHBheW1lbnQgc3VjY2VlZGVkOlwiLCBlcnJvcilcbiAgfVxufVxuXG5hc3luYyBmdW5jdGlvbiBoYW5kbGVQYXltZW50RmFpbGVkKGludm9pY2U6IFN0cmlwZS5JbnZvaWNlKSB7XG4gIHRyeSB7XG4gICAgaWYgKCFpbnZvaWNlLnN1YnNjcmlwdGlvbikgcmV0dXJuXG5cbiAgICBjb25zdCBzdWJzY3JpcHRpb24gPSBhd2FpdCBwcmlzbWEuc3Vic2NyaXB0aW9uLmZpbmRGaXJzdCh7XG4gICAgICB3aGVyZToge1xuICAgICAgICBzdHJpcGVTdWJzY3JpcHRpb25JZDogaW52b2ljZS5zdWJzY3JpcHRpb24gYXMgc3RyaW5nLFxuICAgICAgfSxcbiAgICB9KVxuXG4gICAgaWYgKCFzdWJzY3JpcHRpb24pIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoYFN1YnNjcmlwdGlvbiBub3QgZm91bmQ6ICR7aW52b2ljZS5zdWJzY3JpcHRpb259YClcbiAgICAgIHJldHVyblxuICAgIH1cblxuICAgIC8vIENyZWF0ZSBmYWlsZWQgcGF5bWVudCByZWNvcmRcbiAgICBhd2FpdCBwcmlzbWEucGF5bWVudC5jcmVhdGUoe1xuICAgICAgZGF0YToge1xuICAgICAgICB1c2VySWQ6IHN1YnNjcmlwdGlvbi51c2VySWQsXG4gICAgICAgIHN1YnNjcmlwdGlvbklkOiBzdWJzY3JpcHRpb24uaWQsXG4gICAgICAgIHN0cmlwZVBheW1lbnRJZDogaW52b2ljZS5wYXltZW50X2ludGVudCBhcyBzdHJpbmcsXG4gICAgICAgIGFtb3VudDogaW52b2ljZS5hbW91bnRfZHVlIC8gMTAwLCAvLyBDb252ZXJ0IGZyb20gY2VudHNcbiAgICAgICAgY3VycmVuY3k6IGludm9pY2UuY3VycmVuY3ksXG4gICAgICAgIHN0YXR1czogUGF5bWVudFN0YXR1cy5GQUlMRUQsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBgRmFpbGVkIHN1YnNjcmlwdGlvbiBwYXltZW50IGZvciAke3N1YnNjcmlwdGlvbi50aWVyfWAsXG4gICAgICAgIGZhaWx1cmVSZWFzb246ICdQYXltZW50IGZhaWxlZCcsXG4gICAgICB9LFxuICAgIH0pXG5cbiAgICBjb25zb2xlLmxvZyhgQ3JlYXRlZCBmYWlsZWQgcGF5bWVudCByZWNvcmQgZm9yIHN1YnNjcmlwdGlvbiAke3N1YnNjcmlwdGlvbi5pZH1gKVxuICB9IGNhdGNoIChlcnJvcikge1xuICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBoYW5kbGluZyBwYXltZW50IGZhaWxlZDpcIiwgZXJyb3IpXG4gIH1cbn1cbiJdLCJuYW1lcyI6WyJOZXh0UmVzcG9uc2UiLCJoZWFkZXJzIiwicHJpc21hIiwidmVyaWZ5U3RyaXBlV2ViaG9vayIsIlN1YnNjcmlwdGlvblN0YXR1cyIsIlBheW1lbnRTdGF0dXMiLCJydW50aW1lIiwiUE9TVCIsInJlcXVlc3QiLCJib2R5IiwidGV4dCIsInNpZ25hdHVyZSIsImdldCIsImpzb24iLCJlcnJvciIsInN0YXR1cyIsImV2ZW50IiwiY29uc29sZSIsImxvZyIsInR5cGUiLCJoYW5kbGVTdWJzY3JpcHRpb25VcGRhdGUiLCJkYXRhIiwib2JqZWN0IiwiaGFuZGxlU3Vic2NyaXB0aW9uRGVsZXRlZCIsImhhbmRsZVBheW1lbnRTdWNjZWVkZWQiLCJoYW5kbGVQYXltZW50RmFpbGVkIiwicmVjZWl2ZWQiLCJzdHJpcGVTdWJzY3JpcHRpb24iLCJzdWJzY3JpcHRpb24iLCJmaW5kRmlyc3QiLCJ3aGVyZSIsInN0cmlwZVN1YnNjcmlwdGlvbklkIiwiaWQiLCJBQ1RJVkUiLCJDQU5DRUxFRCIsIlBBU1RfRFVFIiwiVU5QQUlEIiwiSU5DT01QTEVURSIsIklOQ09NUExFVEVfRVhQSVJFRCIsIlRSSUFMSU5HIiwidXBkYXRlIiwiY3VycmVudFBlcmlvZFN0YXJ0IiwiRGF0ZSIsImN1cnJlbnRfcGVyaW9kX3N0YXJ0IiwiY3VycmVudFBlcmlvZEVuZCIsImN1cnJlbnRfcGVyaW9kX2VuZCIsImNhbmNlbEF0UGVyaW9kRW5kIiwiY2FuY2VsX2F0X3BlcmlvZF9lbmQiLCJ0cmlhbFN0YXJ0IiwidHJpYWxfc3RhcnQiLCJ0cmlhbEVuZCIsInRyaWFsX2VuZCIsImNhbmNlbGVkQXQiLCJpbnZvaWNlIiwiaW5jbHVkZSIsInVzZXIiLCJwYXltZW50IiwiY3JlYXRlIiwidXNlcklkIiwic3Vic2NyaXB0aW9uSWQiLCJzdHJpcGVQYXltZW50SWQiLCJwYXltZW50X2ludGVudCIsImFtb3VudCIsImFtb3VudF9wYWlkIiwiY3VycmVuY3kiLCJDT01QTEVURUQiLCJkZXNjcmlwdGlvbiIsInRpZXIiLCJhbW91bnRfZHVlIiwiRkFJTEVEIiwiZmFpbHVyZVJlYXNvbiJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/webhooks/stripe/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/db.ts":
/*!***********************!*\
  !*** ./src/lib/db.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   prisma: () => (/* binding */ prisma)\n/* harmony export */ });\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_0__);\n\nconst globalForPrisma = globalThis;\nconst prisma = globalForPrisma.prisma ?? new _prisma_client__WEBPACK_IMPORTED_MODULE_0__.PrismaClient();\nif (true) globalForPrisma.prisma = prisma;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL2RiLnRzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUE2QztBQUU3QyxNQUFNQyxrQkFBa0JDO0FBSWpCLE1BQU1DLFNBQVNGLGdCQUFnQkUsTUFBTSxJQUFJLElBQUlILHdEQUFZQSxHQUFFO0FBRWxFLElBQUlJLElBQXlCLEVBQWNILGdCQUFnQkUsTUFBTSxHQUFHQSIsInNvdXJjZXMiOlsid2VicGFjazovL2JsYWNrLWJsb2cvLi9zcmMvbGliL2RiLnRzPzllNGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmNvbnN0IGdsb2JhbEZvclByaXNtYSA9IGdsb2JhbFRoaXMgYXMgdW5rbm93biBhcyB7XG4gIHByaXNtYTogUHJpc21hQ2xpZW50IHwgdW5kZWZpbmVkXG59XG5cbmV4cG9ydCBjb25zdCBwcmlzbWEgPSBnbG9iYWxGb3JQcmlzbWEucHJpc21hID8/IG5ldyBQcmlzbWFDbGllbnQoKVxuXG5pZiAocHJvY2Vzcy5lbnYuTk9ERV9FTlYgIT09ICdwcm9kdWN0aW9uJykgZ2xvYmFsRm9yUHJpc21hLnByaXNtYSA9IHByaXNtYSJdLCJuYW1lcyI6WyJQcmlzbWFDbGllbnQiLCJnbG9iYWxGb3JQcmlzbWEiLCJnbG9iYWxUaGlzIiwicHJpc21hIiwicHJvY2VzcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/db.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/stripe.ts":
/*!***************************!*\
  !*** ./src/lib/stripe.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   REVENUE_SHARE: () => (/* binding */ REVENUE_SHARE),\n/* harmony export */   STRIPE_PRICE_IDS: () => (/* binding */ STRIPE_PRICE_IDS),\n/* harmony export */   SUBSCRIPTION_TIERS: () => (/* binding */ SUBSCRIPTION_TIERS),\n/* harmony export */   calculateRevenueSplit: () => (/* binding */ calculateRevenueSplit),\n/* harmony export */   formatCurrency: () => (/* binding */ formatCurrency),\n/* harmony export */   getStripe: () => (/* binding */ getStripe),\n/* harmony export */   stripe: () => (/* binding */ stripe),\n/* harmony export */   verifyStripeWebhook: () => (/* binding */ verifyStripeWebhook)\n/* harmony export */ });\n/* harmony import */ var stripe__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! stripe */ \"(rsc)/./node_modules/stripe/esm/stripe.esm.node.js\");\n/* harmony import */ var _stripe_stripe_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @stripe/stripe-js */ \"(rsc)/./node_modules/@stripe/stripe-js/lib/index.mjs\");\n\n\n// Server-side Stripe instance\nconst stripe = new stripe__WEBPACK_IMPORTED_MODULE_0__[\"default\"](process.env.STRIPE_SECRET_KEY, {\n    apiVersion: \"2024-06-20\",\n    typescript: true\n});\n// Client-side Stripe instance\nlet stripePromise;\nconst getStripe = ()=>{\n    if (!stripePromise) {\n        stripePromise = (0,_stripe_stripe_js__WEBPACK_IMPORTED_MODULE_1__.loadStripe)(\"pk_test_51RZ18cE7j6iXdCGyykJ9YwinC6IlOmaUNxcuMdDDWaNuzugT4uhBIbQ3dg1bxb5J8DYUVnepXotfEswjis0Z0knL00Tmsa0Jlj\");\n    }\n    return stripePromise;\n};\n// Stripe webhook signature verification\nconst verifyStripeWebhook = (payload, signature)=>{\n    const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;\n    return stripe.webhooks.constructEvent(payload, signature, webhookSecret);\n};\n// Subscription tier configurations\nconst SUBSCRIPTION_TIERS = {\n    FREE: {\n        name: \"Free\",\n        description: \"Access to free content only\",\n        price: 0,\n        yearlyPrice: 0,\n        features: [\n            \"Read free novels and chapters\",\n            \"Basic reading progress tracking\",\n            \"Add to library\"\n        ]\n    },\n    PREMIUM: {\n        name: \"Premium\",\n        description: \"Access to premium content and features\",\n        price: 9.99,\n        yearlyPrice: 99.99,\n        features: [\n            \"All free features\",\n            \"Access to premium novels and chapters\",\n            \"Ad-free reading experience\",\n            \"Early access to new chapters\",\n            \"Support your favorite authors\"\n        ]\n    },\n    PREMIUM_PLUS: {\n        name: \"Premium Plus\",\n        description: \"All premium features plus exclusive content\",\n        price: 19.99,\n        yearlyPrice: 199.99,\n        features: [\n            \"All premium features\",\n            \"Exclusive premium plus content\",\n            \"Direct messaging with authors\",\n            \"Priority customer support\",\n            \"Beta access to new features\"\n        ]\n    }\n};\n// Revenue sharing configuration\nconst REVENUE_SHARE = {\n    PLATFORM_PERCENTAGE: 30,\n    AUTHOR_PERCENTAGE: 70,\n    MINIMUM_PAYOUT_AMOUNT: 50\n};\n// Stripe price IDs (these would be set in environment variables in production)\nconst STRIPE_PRICE_IDS = {\n    PREMIUM_MONTHLY: process.env.STRIPE_PREMIUM_MONTHLY_PRICE_ID,\n    PREMIUM_YEARLY: process.env.STRIPE_PREMIUM_YEARLY_PRICE_ID,\n    PREMIUM_PLUS_MONTHLY: process.env.STRIPE_PREMIUM_PLUS_MONTHLY_PRICE_ID,\n    PREMIUM_PLUS_YEARLY: process.env.STRIPE_PREMIUM_PLUS_YEARLY_PRICE_ID\n};\n// Helper functions\nconst formatCurrency = (amount, currency = \"USD\")=>{\n    return new Intl.NumberFormat(\"en-US\", {\n        style: \"currency\",\n        currency\n    }).format(amount);\n};\nconst calculateRevenueSplit = (amount)=>{\n    const platformFee = amount * REVENUE_SHARE.PLATFORM_PERCENTAGE / 100;\n    const authorEarning = amount - platformFee;\n    return {\n        platformFee,\n        authorEarning\n    };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/stripe.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/stripe","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/get-proto","vendor-chunks/@stripe","vendor-chunks/object-inspect","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/hasown","vendor-chunks/get-intrinsic","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwebhooks%2Fstripe%2Froute&page=%2Fapi%2Fwebhooks%2Fstripe%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwebhooks%2Fstripe%2Froute.ts&appDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fweerawat%2FDesktop%2Fadc-platform%2Fservices%2Fcontent%2Fblack-blog&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();