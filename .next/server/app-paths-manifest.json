{"/browse/page": "app/browse/page.js", "/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/novels/[id]/page": "app/novels/[id]/page.js", "/authors/[id]/page": "app/authors/[id]/page.js", "/api/authors/[id]/route": "app/api/authors/[id]/route.js", "/settings/page": "app/settings/page.js", "/api/user/profile/route": "app/api/user/profile/route.js", "/profile/page": "app/profile/page.js", "/dashboard/page": "app/dashboard/page.js", "/api/novels/author/route": "app/api/novels/author/route.js"}