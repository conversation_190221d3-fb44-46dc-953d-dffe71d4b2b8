"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"85a0d896d578\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjg1YTBkODk2ZDU3OFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: function() { return /* binding */ store; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @reduxjs/toolkit/query */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n/* harmony import */ var _store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/store/api/novelsApi */ \"(app-pages-browser)/./src/store/api/novelsApi.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* harmony import */ var _store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/api/libraryApi */ \"(app-pages-browser)/./src/store/api/libraryApi.ts\");\n/* harmony import */ var _store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/api/authorsApi */ \"(app-pages-browser)/./src/store/api/authorsApi.ts\");\n/* harmony import */ var _store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/api/subscriptionsApi */ \"(app-pages-browser)/./src/store/api/subscriptionsApi.ts\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(app-pages-browser)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _store_slices_uiSlice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/slices/uiSlice */ \"(app-pages-browser)/./src/store/slices/uiSlice.ts\");\n\n\n\n\n\n\n\n\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_7__.configureStore)({\n    reducer: {\n        // API slices\n        [_store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__.novelsApi.reducerPath]: _store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__.novelsApi.reducer,\n        [_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__.chaptersApi.reducerPath]: _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__.chaptersApi.reducer,\n        [_store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__.libraryApi.reducerPath]: _store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__.libraryApi.reducer,\n        [_store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__.authorsApi.reducerPath]: _store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__.authorsApi.reducer,\n        [_store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__.subscriptionsApi.reducerPath]: _store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__.subscriptionsApi.reducer,\n        // Regular slices\n        auth: _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_5__.authSlice.reducer,\n        ui: _store_slices_uiSlice__WEBPACK_IMPORTED_MODULE_6__.uiSlice.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware().concat(_store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__.novelsApi.middleware, _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__.chaptersApi.middleware, _store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__.libraryApi.middleware, _store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__.authorsApi.middleware, _store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__.subscriptionsApi.middleware)\n});\n(0,_reduxjs_toolkit_query__WEBPACK_IMPORTED_MODULE_8__.setupListeners)(store.dispatch);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/store.ts\n"));

/***/ })

});