"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"bb1a74050f32\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6IjtBQUFBLCtEQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvZ2xvYmFscy5jc3M/NjVjZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImJiMWE3NDA1MGYzMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/store.ts":
/*!**************************!*\
  !*** ./src/lib/store.ts ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   store: function() { return /* binding */ store; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @reduxjs/toolkit */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @reduxjs/toolkit/query */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n/* harmony import */ var _store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/store/api/novelsApi */ \"(app-pages-browser)/./src/store/api/novelsApi.ts\");\n/* harmony import */ var _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/store/api/chaptersApi */ \"(app-pages-browser)/./src/store/api/chaptersApi.ts\");\n/* harmony import */ var _store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/store/api/libraryApi */ \"(app-pages-browser)/./src/store/api/libraryApi.ts\");\n/* harmony import */ var _store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/store/api/authorsApi */ \"(app-pages-browser)/./src/store/api/authorsApi.ts\");\n/* harmony import */ var _store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/store/api/subscriptionsApi */ \"(app-pages-browser)/./src/store/api/subscriptionsApi.ts\");\n/* harmony import */ var _store_api_earningsApi__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/store/api/earningsApi */ \"(app-pages-browser)/./src/store/api/earningsApi.ts\");\n/* harmony import */ var _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/store/slices/authSlice */ \"(app-pages-browser)/./src/store/slices/authSlice.ts\");\n/* harmony import */ var _store_slices_uiSlice__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/store/slices/uiSlice */ \"(app-pages-browser)/./src/store/slices/uiSlice.ts\");\n\n\n\n\n\n\n\n\n\n\nconst store = (0,_reduxjs_toolkit__WEBPACK_IMPORTED_MODULE_8__.configureStore)({\n    reducer: {\n        // API slices\n        [_store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__.novelsApi.reducerPath]: _store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__.novelsApi.reducer,\n        [_store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__.chaptersApi.reducerPath]: _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__.chaptersApi.reducer,\n        [_store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__.libraryApi.reducerPath]: _store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__.libraryApi.reducer,\n        [_store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__.authorsApi.reducerPath]: _store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__.authorsApi.reducer,\n        [_store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__.subscriptionsApi.reducerPath]: _store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__.subscriptionsApi.reducer,\n        [_store_api_earningsApi__WEBPACK_IMPORTED_MODULE_5__.earningsApi.reducerPath]: _store_api_earningsApi__WEBPACK_IMPORTED_MODULE_5__.earningsApi.reducer,\n        // Regular slices\n        auth: _store_slices_authSlice__WEBPACK_IMPORTED_MODULE_6__.authSlice.reducer,\n        ui: _store_slices_uiSlice__WEBPACK_IMPORTED_MODULE_7__.uiSlice.reducer\n    },\n    middleware: (getDefaultMiddleware)=>getDefaultMiddleware().concat(_store_api_novelsApi__WEBPACK_IMPORTED_MODULE_0__.novelsApi.middleware, _store_api_chaptersApi__WEBPACK_IMPORTED_MODULE_1__.chaptersApi.middleware, _store_api_libraryApi__WEBPACK_IMPORTED_MODULE_2__.libraryApi.middleware, _store_api_authorsApi__WEBPACK_IMPORTED_MODULE_3__.authorsApi.middleware, _store_api_subscriptionsApi__WEBPACK_IMPORTED_MODULE_4__.subscriptionsApi.middleware, _store_api_earningsApi__WEBPACK_IMPORTED_MODULE_5__.earningsApi.middleware)\n});\n(0,_reduxjs_toolkit_query__WEBPACK_IMPORTED_MODULE_9__.setupListeners)(store.dispatch);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/store.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/store/api/earningsApi.ts":
/*!**************************************!*\
  !*** ./src/store/api/earningsApi.ts ***!
  \**************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   earningsApi: function() { return /* binding */ earningsApi; },\n/* harmony export */   useGetEarningsAnalyticsQuery: function() { return /* binding */ useGetEarningsAnalyticsQuery; },\n/* harmony export */   useGetEarningsQuery: function() { return /* binding */ useGetEarningsQuery; }\n/* harmony export */ });\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/react/rtk-query-react.modern.mjs\");\n/* harmony import */ var _reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @reduxjs/toolkit/query/react */ \"(app-pages-browser)/./node_modules/@reduxjs/toolkit/dist/query/rtk-query.modern.mjs\");\n\nconst earningsApi = (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_0__.createApi)({\n    reducerPath: \"earningsApi\",\n    baseQuery: (0,_reduxjs_toolkit_query_react__WEBPACK_IMPORTED_MODULE_1__.fetchBaseQuery)({\n        baseUrl: \"/api/earnings\"\n    }),\n    tagTypes: [\n        \"Earnings\",\n        \"EarningsAnalytics\"\n    ],\n    endpoints: (builder)=>({\n            // Get earnings with pagination and filtering\n            getEarnings: builder.query({\n                query: function() {\n                    let params = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n                    var _params_page, _params_limit;\n                    return {\n                        url: \"\",\n                        params: {\n                            page: (_params_page = params.page) === null || _params_page === void 0 ? void 0 : _params_page.toString(),\n                            limit: (_params_limit = params.limit) === null || _params_limit === void 0 ? void 0 : _params_limit.toString(),\n                            type: params.type,\n                            startDate: params.startDate,\n                            endDate: params.endDate\n                        }\n                    };\n                },\n                providesTags: [\n                    \"Earnings\"\n                ]\n            }),\n            // Get earnings analytics\n            getEarningsAnalytics: builder.query({\n                query: ()=>\"/analytics\",\n                providesTags: [\n                    \"EarningsAnalytics\"\n                ]\n            })\n        })\n});\nconst { useGetEarningsQuery, useGetEarningsAnalyticsQuery } = earningsApi;\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9zdG9yZS9hcGkvZWFybmluZ3NBcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7QUFBd0U7QUF1Q2pFLE1BQU1FLGNBQWNGLHVFQUFTQSxDQUFDO0lBQ25DRyxhQUFhO0lBQ2JDLFdBQVdILDRFQUFjQSxDQUFDO1FBQ3hCSSxTQUFTO0lBQ1g7SUFDQUMsVUFBVTtRQUFDO1FBQVk7S0FBb0I7SUFDM0NDLFdBQVcsQ0FBQ0MsVUFBYTtZQUN2Qiw2Q0FBNkM7WUFDN0NDLGFBQWFELFFBQVFFLEtBQUssQ0FBd0M7Z0JBQ2hFQSxPQUFPO3dCQUFDQywwRUFBUyxDQUFDO3dCQUdSQSxjQUNDQTsyQkFKYzt3QkFDdkJDLEtBQUs7d0JBQ0xELFFBQVE7NEJBQ05FLElBQUksR0FBRUYsZUFBQUEsT0FBT0UsSUFBSSxjQUFYRixtQ0FBQUEsYUFBYUcsUUFBUTs0QkFDM0JDLEtBQUssR0FBRUosZ0JBQUFBLE9BQU9JLEtBQUssY0FBWkosb0NBQUFBLGNBQWNHLFFBQVE7NEJBQzdCRSxNQUFNTCxPQUFPSyxJQUFJOzRCQUNqQkMsV0FBV04sT0FBT00sU0FBUzs0QkFDM0JDLFNBQVNQLE9BQU9PLE9BQU87d0JBQ3pCO29CQUNGOztnQkFDQUMsY0FBYztvQkFBQztpQkFBVztZQUM1QjtZQUVBLHlCQUF5QjtZQUN6QkMsc0JBQXNCWixRQUFRRSxLQUFLLENBQTBCO2dCQUMzREEsT0FBTyxJQUFNO2dCQUNiUyxjQUFjO29CQUFDO2lCQUFvQjtZQUNyQztRQUNGO0FBQ0YsR0FBRTtBQUVLLE1BQU0sRUFDWEUsbUJBQW1CLEVBQ25CQyw0QkFBNEIsRUFDN0IsR0FBR3BCLFlBQVciLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL3N0b3JlL2FwaS9lYXJuaW5nc0FwaS50cz8zOTMzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZUFwaSwgZmV0Y2hCYXNlUXVlcnkgfSBmcm9tICdAcmVkdXhqcy90b29sa2l0L3F1ZXJ5L3JlYWN0J1xuaW1wb3J0IHR5cGUgeyBFYXJuaW5nLCBQYXlvdXQsIEVhcm5pbmdUeXBlIH0gZnJvbSAnQHByaXNtYS9jbGllbnQnXG5cbmV4cG9ydCBpbnRlcmZhY2UgRWFybmluZ1dpdGhQYXlvdXQgZXh0ZW5kcyBFYXJuaW5nIHtcbiAgcGF5b3V0PzogUGljazxQYXlvdXQsICdpZCcgfCAnc3RhdHVzJyB8ICdwcm9jZXNzZWRBdCc+IHwgbnVsbFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIEVhcm5pbmdzUmVzcG9uc2Uge1xuICBkYXRhOiBFYXJuaW5nV2l0aFBheW91dFtdXG4gIHBhZ2luYXRpb246IHtcbiAgICBwYWdlOiBudW1iZXJcbiAgICBsaW1pdDogbnVtYmVyXG4gICAgdG90YWw6IG51bWJlclxuICAgIHBhZ2VzOiBudW1iZXJcbiAgICBoYXNOZXh0OiBib29sZWFuXG4gICAgaGFzUHJldjogYm9vbGVhblxuICB9XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgRWFybmluZ3NBbmFseXRpY3Mge1xuICB0b3RhbEVhcm5pbmdzOiBudW1iZXJcbiAgbW9udGhseUVhcm5pbmdzOiBudW1iZXJcbiAgcGVuZGluZ1BheW91dHM6IG51bWJlclxuICBjb21wbGV0ZWRQYXlvdXRzOiBudW1iZXJcbiAgZWFybmluZ3NCeVR5cGU6IFJlY29yZDxFYXJuaW5nVHlwZSwgbnVtYmVyPlxuICBlYXJuaW5nc0J5TW9udGg6IEFycmF5PHtcbiAgICBtb250aDogc3RyaW5nXG4gICAgYW1vdW50OiBudW1iZXJcbiAgfT5cbn1cblxuZXhwb3J0IGludGVyZmFjZSBFYXJuaW5nc1F1ZXJ5UGFyYW1zIHtcbiAgcGFnZT86IG51bWJlclxuICBsaW1pdD86IG51bWJlclxuICB0eXBlPzogRWFybmluZ1R5cGVcbiAgc3RhcnREYXRlPzogc3RyaW5nXG4gIGVuZERhdGU/OiBzdHJpbmdcbn1cblxuZXhwb3J0IGNvbnN0IGVhcm5pbmdzQXBpID0gY3JlYXRlQXBpKHtcbiAgcmVkdWNlclBhdGg6ICdlYXJuaW5nc0FwaScsXG4gIGJhc2VRdWVyeTogZmV0Y2hCYXNlUXVlcnkoe1xuICAgIGJhc2VVcmw6ICcvYXBpL2Vhcm5pbmdzJyxcbiAgfSksXG4gIHRhZ1R5cGVzOiBbJ0Vhcm5pbmdzJywgJ0Vhcm5pbmdzQW5hbHl0aWNzJ10sXG4gIGVuZHBvaW50czogKGJ1aWxkZXIpID0+ICh7XG4gICAgLy8gR2V0IGVhcm5pbmdzIHdpdGggcGFnaW5hdGlvbiBhbmQgZmlsdGVyaW5nXG4gICAgZ2V0RWFybmluZ3M6IGJ1aWxkZXIucXVlcnk8RWFybmluZ3NSZXNwb25zZSwgRWFybmluZ3NRdWVyeVBhcmFtcz4oe1xuICAgICAgcXVlcnk6IChwYXJhbXMgPSB7fSkgPT4gKHtcbiAgICAgICAgdXJsOiAnJyxcbiAgICAgICAgcGFyYW1zOiB7XG4gICAgICAgICAgcGFnZTogcGFyYW1zLnBhZ2U/LnRvU3RyaW5nKCksXG4gICAgICAgICAgbGltaXQ6IHBhcmFtcy5saW1pdD8udG9TdHJpbmcoKSxcbiAgICAgICAgICB0eXBlOiBwYXJhbXMudHlwZSxcbiAgICAgICAgICBzdGFydERhdGU6IHBhcmFtcy5zdGFydERhdGUsXG4gICAgICAgICAgZW5kRGF0ZTogcGFyYW1zLmVuZERhdGUsXG4gICAgICAgIH0sXG4gICAgICB9KSxcbiAgICAgIHByb3ZpZGVzVGFnczogWydFYXJuaW5ncyddLFxuICAgIH0pLFxuXG4gICAgLy8gR2V0IGVhcm5pbmdzIGFuYWx5dGljc1xuICAgIGdldEVhcm5pbmdzQW5hbHl0aWNzOiBidWlsZGVyLnF1ZXJ5PEVhcm5pbmdzQW5hbHl0aWNzLCB2b2lkPih7XG4gICAgICBxdWVyeTogKCkgPT4gJy9hbmFseXRpY3MnLFxuICAgICAgcHJvdmlkZXNUYWdzOiBbJ0Vhcm5pbmdzQW5hbHl0aWNzJ10sXG4gICAgfSksXG4gIH0pLFxufSlcblxuZXhwb3J0IGNvbnN0IHtcbiAgdXNlR2V0RWFybmluZ3NRdWVyeSxcbiAgdXNlR2V0RWFybmluZ3NBbmFseXRpY3NRdWVyeSxcbn0gPSBlYXJuaW5nc0FwaVxuIl0sIm5hbWVzIjpbImNyZWF0ZUFwaSIsImZldGNoQmFzZVF1ZXJ5IiwiZWFybmluZ3NBcGkiLCJyZWR1Y2VyUGF0aCIsImJhc2VRdWVyeSIsImJhc2VVcmwiLCJ0YWdUeXBlcyIsImVuZHBvaW50cyIsImJ1aWxkZXIiLCJnZXRFYXJuaW5ncyIsInF1ZXJ5IiwicGFyYW1zIiwidXJsIiwicGFnZSIsInRvU3RyaW5nIiwibGltaXQiLCJ0eXBlIiwic3RhcnREYXRlIiwiZW5kRGF0ZSIsInByb3ZpZGVzVGFncyIsImdldEVhcm5pbmdzQW5hbHl0aWNzIiwidXNlR2V0RWFybmluZ3NRdWVyeSIsInVzZUdldEVhcm5pbmdzQW5hbHl0aWNzUXVlcnkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/store/api/earningsApi.ts\n"));

/***/ })

});