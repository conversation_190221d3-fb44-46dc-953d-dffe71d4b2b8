# Database (Update with your Supabase credentials from 'supabase status')
DATABASE_URL="postgresql://postgres:postgres@localhost:54322/postgres"
DIRECT_URL="postgresql://postgres:postgres@localhost:54322/postgres"

# NextAuth
NEXTAUTH_SECRET="your-nextauth-secret-at-least-32-characters-long"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth (Get from Google Cloud Console)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Supabase Local (Update with your local Supabase credentials from 'supabase status')
NEXT_PUBLIC_SUPABASE_URL="http://localhost:54321"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-local-supabase-anon-key"
SUPABASE_SERVICE_ROLE_KEY="your-local-supabase-service-role-key"

# Production Supabase (uncomment and update for production deployment)
# NEXT_PUBLIC_SUPABASE_URL="https://your-project-ref.supabase.co"
# NEXT_PUBLIC_SUPABASE_ANON_KEY="your-production-anon-key"
# SUPABASE_SERVICE_ROLE_KEY="your-production-service-role-key"
# DATABASE_URL="postgresql://postgres:[password]@db.[project-ref].supabase.co:5432/postgres"

# App Configuration
NEXT_PUBLIC_APP_URL="http://localhost:3000"
NEXT_PUBLIC_APP_NAME="Black Blog"

# Stripe Configuration
STRIPE_SECRET_KEY="sk_test_your_stripe_secret_key_here"
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_your_stripe_publishable_key_here"
STRIPE_WEBHOOK_SECRET="whsec_your_webhook_signing_secret_here"

# Stripe Price IDs (create these in your Stripe Dashboard)
STRIPE_PREMIUM_MONTHLY_PRICE_ID="price_your_premium_monthly_price_id"
STRIPE_PREMIUM_YEARLY_PRICE_ID="price_your_premium_yearly_price_id"
STRIPE_PREMIUM_PLUS_MONTHLY_PRICE_ID="price_your_premium_plus_monthly_price_id"
STRIPE_PREMIUM_PLUS_YEARLY_PRICE_ID="price_your_premium_plus_yearly_price_id"