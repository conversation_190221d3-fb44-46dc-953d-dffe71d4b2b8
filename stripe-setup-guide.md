# Stripe Payment System Configuration Guide

## Overview
This guide will help you complete the Stripe integration for your monetization platform.

## Step 1: Stripe Account Setup

### 1.1 Create/Access Stripe Account
1. Go to [https://dashboard.stripe.com](https://dashboard.stripe.com)
2. Sign in or create a new account
3. Switch to **Test Mode** for development (toggle in top-right)

### 1.2 Get API Keys
1. Navigate to **Developers > API keys**
2. Copy the following keys:
   - **Publishable key** (starts with `pk_test_`)
   - **Secret key** (starts with `sk_test_`)

## Step 2: Create Subscription Products

### 2.1 Create Premium Product
1. Go to **Products** in Stripe Dashboard
2. Click **+ Add product**
3. Fill in:
   - **Name**: Premium Subscription
   - **Description**: Access to premium novels and ad-free reading
4. Click **Save product**

### 2.2 Create Premium Prices
For the Premium product, create two prices:

**Monthly Price:**
- **Price**: $9.99
- **Billing period**: Monthly
- **Currency**: USD
- Copy the **Price ID** (starts with `price_`)

**Yearly Price:**
- **Price**: $99.99 (16% discount)
- **Billing period**: Yearly
- **Currency**: USD
- Copy the **Price ID** (starts with `price_`)

### 2.3 Create Premium Plus Product
1. Click **+ Add product**
2. Fill in:
   - **Name**: Premium Plus Subscription
   - **Description**: All premium features plus exclusive content and direct author messaging
3. Click **Save product**

### 2.4 Create Premium Plus Prices
For the Premium Plus product, create two prices:

**Monthly Price:**
- **Price**: $19.99
- **Billing period**: Monthly
- **Currency**: USD
- Copy the **Price ID** (starts with `price_`)

**Yearly Price:**
- **Price**: $199.99 (16% discount)
- **Billing period**: Yearly
- **Currency**: USD
- Copy the **Price ID** (starts with `price_`)

## Step 3: Configure Webhooks

### 3.1 Create Webhook Endpoint
1. Go to **Developers > Webhooks**
2. Click **+ Add endpoint**
3. Fill in:
   - **Endpoint URL**: `http://localhost:3000/api/webhooks/stripe` (for development)
   - **Description**: Monetization Platform Webhooks

### 3.2 Select Events
Add these events to listen for:
- `customer.subscription.created`
- `customer.subscription.updated`
- `customer.subscription.deleted`
- `invoice.payment_succeeded`
- `invoice.payment_failed`
- `checkout.session.completed`

### 3.3 Get Webhook Secret
1. Click on your created webhook
2. Click **Reveal** next to **Signing secret**
3. Copy the webhook signing secret (starts with `whsec_`)

## Step 4: Environment Variables

Create or update your `.env.local` file with the following variables:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_publishable_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Stripe Price IDs (replace with your actual price IDs)
STRIPE_PREMIUM_MONTHLY_PRICE_ID=price_your_premium_monthly_id
STRIPE_PREMIUM_YEARLY_PRICE_ID=price_your_premium_yearly_id
STRIPE_PREMIUM_PLUS_MONTHLY_PRICE_ID=price_your_premium_plus_monthly_id
STRIPE_PREMIUM_PLUS_YEARLY_PRICE_ID=price_your_premium_plus_yearly_id

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

## Step 5: Database Setup

Run the following commands to set up your database:

```bash
# Generate Prisma client with new schema
npx prisma generate

# Push schema to database
npx prisma db push

# (Optional) Seed initial subscription tier configurations
npx prisma db seed
```

## Step 6: Test the Integration

### 6.1 Start Development Server
```bash
npm run dev
```

### 6.2 Test Subscription Flow
1. Navigate to `http://localhost:3000/pricing`
2. Try to subscribe to a plan
3. Use Stripe test card: `4242 4242 4242 4242`
4. Check webhook events in Stripe Dashboard

### 6.3 Test Webhook Events
1. Go to **Developers > Webhooks** in Stripe Dashboard
2. Click on your webhook endpoint
3. Check the **Recent deliveries** section
4. Verify events are being received successfully

## Step 7: Stripe Connect Setup (for Author Payouts)

### 7.1 Enable Connect
1. Go to **Connect** in Stripe Dashboard
2. Click **Get started**
3. Fill in your platform information
4. Enable **Express accounts** for authors

### 7.2 Configure Connect Settings
1. Set **Account requirements** to minimal for testing
2. Configure **Branding** for the onboarding flow
3. Set up **Webhooks** for Connect events

## Troubleshooting

### Common Issues:

1. **Webhook not receiving events**:
   - Check the endpoint URL is correct
   - Verify the webhook secret in environment variables
   - Check server logs for errors

2. **Payment fails**:
   - Verify API keys are correct
   - Check price IDs match your Stripe products
   - Ensure test mode is enabled

3. **Database connection issues**:
   - Verify `DATABASE_URL` and `DIRECT_URL` in `.env.local`
   - Run `npx prisma db push` to sync schema

### Test Cards:
- **Success**: `4242 4242 4242 4242`
- **Decline**: `4000 0000 0000 0002`
- **3D Secure**: `4000 0025 0000 3155`

## Next Steps

After completing this setup:
1. Test the complete subscription flow
2. Verify webhook event processing
3. Test author payout functionality
4. Configure production environment when ready

For production deployment:
1. Switch to live mode in Stripe
2. Update webhook URL to production domain
3. Update environment variables with live keys
4. Test thoroughly before going live
